using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using DateFactoryApp.Data;
using DateFactoryApp.Services;
using DateFactoryApp.ViewModels;
using System.Windows;
using Serilog;

namespace DateFactoryApp
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/app-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // Build host
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices(ConfigureServices)
                .Build();

            // Initialize database
            using (var scope = _host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                context.Database.EnsureCreated();
            }

            base.OnStartup(e);
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // Database
            services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite("Data Source=DateFactory.db"));

            // Services
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<ISalesService, SalesService>();
            services.AddSingleton<IInventoryService, InventoryService>();
            services.AddSingleton<ICustomerService, CustomerService>();
            services.AddSingleton<ISupplierService, SupplierService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<IBackupService, BackupService>();
            services.AddSingleton<ISyncService, SyncService>();
            services.AddSingleton<IThemeService, ThemeService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<ProductsViewModel>();
            services.AddTransient<SalesViewModel>();
            services.AddTransient<InventoryViewModel>();
            services.AddTransient<CustomersViewModel>();
            services.AddTransient<SuppliersViewModel>();
            services.AddTransient<ReportsViewModel>();
            services.AddTransient<SettingsViewModel>();
        }

        public static T GetService<T>() where T : class
        {
            return ((App)Current)._host?.Services.GetService<T>()!;
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
