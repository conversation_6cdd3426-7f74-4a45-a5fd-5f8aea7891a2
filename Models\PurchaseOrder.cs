using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class PurchaseOrder : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string OrderNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime OrderDate { get; set; } = DateTime.Now;
        
        [Required]
        public int SupplierId { get; set; }
        
        [Required]
        public int UserId { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaidAmount { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal RemainingAmount { get; set; } = 0;
        
        [Required]
        public PurchaseOrderStatus Status { get; set; } = PurchaseOrderStatus.Draft;
        
        public DateTime? ExpectedDeliveryDate { get; set; }
        
        public DateTime? ActualDeliveryDate { get; set; }
        
        [MaxLength(500)]
        public string? Notes { get; set; }
        
        // Navigation properties
        public virtual Supplier Supplier { get; set; } = null!;
        public virtual User User { get; set; } = null!;
        public virtual ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
    }

    public class PurchaseOrderItem : BaseEntity
    {
        [Required]
        public int PurchaseOrderId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal OrderedQuantity { get; set; }
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal ReceivedQuantity { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;
        
        // Navigation properties
        public virtual PurchaseOrder PurchaseOrder { get; set; } = null!;
        public virtual Product Product { get; set; } = null!;
    }

    public enum PurchaseOrderStatus
    {
        Draft = 1,          // مسودة
        Sent = 2,           // مرسل
        Confirmed = 3,      // مؤكد
        PartiallyReceived = 4, // مستلم جزئياً
        Received = 5,       // مستلم
        Cancelled = 6       // ملغي
    }
}
