<Window x:Class="DateFactoryApp.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة مصنع ومحل التمور"
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,16" 
                           Background="{StaticResource PrimaryBrush}">
            <StackPanel Orientation="Vertical" Padding="24" HorizontalAlignment="Center">
                <materialDesign:PackIcon Kind="Factory" Width="64" Height="64" 
                                       Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="نظام إدارة مصنع ومحل التمور" 
                         Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                         Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                <TextBlock Text="الإصدار 1.0" 
                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                         Foreground="White" HorizontalAlignment="Center" Opacity="0.8"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="24,0" Padding="24">
            <StackPanel Orientation="Vertical">
                <TextBlock Text="تسجيل الدخول" 
                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                         HorizontalAlignment="Center" Margin="0,0,0,24"/>

                <!-- Username -->
                <TextBox x:Name="UsernameTextBox"
                       materialDesign:HintAssist.Hint="اسم المستخدم"
                       materialDesign:HintAssist.IsFloating="True"
                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                       Margin="0,8"
                       Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="كلمة المرور"
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                           Margin="0,8"/>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني" Margin="0,16,0,8"
                        IsChecked="{Binding RememberMe}"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                         Foreground="{StaticResource ErrorBrush}"
                         Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"
                         TextWrapping="Wrap"
                         Margin="0,8"/>

                <!-- Login Button -->
                <Button Content="دخول" 
                      Style="{StaticResource PrimaryButton}"
                      Command="{Binding LoginCommand}"
                      CommandParameter="{Binding ElementName=PasswordBox}"
                      IsDefault="True"
                      Margin="0,16,0,8"/>

                <!-- Loading Indicator -->
                <ProgressBar IsIndeterminate="True" 
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                           Margin="0,8"/>

                <!-- Additional Options -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,16,0,0">
                    <Button Content="إعدادات الاتصال" 
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Command="{Binding ShowConnectionSettingsCommand}"
                          Foreground="{StaticResource SecondaryBrush}"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Vertical" HorizontalAlignment="Center" Margin="24">
            <TextBlock Text="© 2024 نظام إدارة مصنع ومحل التمور" 
                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                     HorizontalAlignment="Center"/>
            <TextBlock Text="جميع الحقوق محفوظة" 
                     Style="{StaticResource MaterialDesignCaptionTextBlock}"
                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                     HorizontalAlignment="Center"/>
        </StackPanel>
    </Grid>
</Window>
