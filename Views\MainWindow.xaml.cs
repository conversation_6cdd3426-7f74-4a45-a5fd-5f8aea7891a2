using System.Windows;
using DateFactoryApp.ViewModels;

namespace DateFactoryApp.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();

            // Create services manually for now
            var userService = new DateFactoryApp.Services.UserService(null!);
            var themeService = new DateFactoryApp.Services.ThemeService();
            var syncService = new DateFactoryApp.Services.SyncService(null!);

            DataContext = new MainViewModel(userService, themeService, syncService);
        }
    }
}
