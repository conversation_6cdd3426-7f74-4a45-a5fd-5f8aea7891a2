using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;

        public SupplierService(AppDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => !s.IsDeleted)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Supplier>> GetActiveSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => !s.IsDeleted && s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
        }

        public async Task<Supplier?> GetSupplierByCodeAsync(string code)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Code == code && !s.IsDeleted);
        }

        public async Task<Supplier> CreateSupplierAsync(Supplier supplier)
        {
            var currentUser = await _userService.GetCurrentUserAsync();

            supplier.CreatedBy = currentUser?.Username;
            supplier.CreatedAt = DateTime.Now;

            if (string.IsNullOrEmpty(supplier.Code))
            {
                supplier.Code = await GenerateSupplierCodeAsync();
            }

            _context.Suppliers.Add(supplier);
            await _context.SaveChangesAsync();
            return supplier;
        }

        public async Task<Supplier> UpdateSupplierAsync(Supplier supplier)
        {
            var existingSupplier = await _context.Suppliers.FindAsync(supplier.Id);
            if (existingSupplier == null)
                throw new ArgumentException("المورد غير موجود");

            var currentUser = await _userService.GetCurrentUserAsync();

            existingSupplier.Name = supplier.Name;
            existingSupplier.Phone = supplier.Phone;
            existingSupplier.Email = supplier.Email;
            existingSupplier.Address = supplier.Address;
            existingSupplier.City = supplier.City;
            existingSupplier.Region = supplier.Region;
            existingSupplier.ContactPerson = supplier.ContactPerson;
            existingSupplier.ContactPhone = supplier.ContactPhone;
            existingSupplier.Type = supplier.Type;
            existingSupplier.IsActive = supplier.IsActive;
            existingSupplier.Notes = supplier.Notes;
            existingSupplier.PaymentTerms = supplier.PaymentTerms;
            existingSupplier.UpdatedAt = DateTime.Now;
            existingSupplier.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return existingSupplier;
        }

        public async Task<bool> DeleteSupplierAsync(int id)
        {
            var supplier = await _context.Suppliers.FindAsync(id);
            if (supplier == null)
                return false;

            // Check if supplier has purchase orders
            var hasPurchaseOrders = await _context.PurchaseOrders.AnyAsync(po => po.SupplierId == id && !po.IsDeleted);
            if (hasPurchaseOrders)
                throw new InvalidOperationException("لا يمكن حذف المورد لأنه يحتوي على أوامر شراء");

            var currentUser = await _userService.GetCurrentUserAsync();

            supplier.IsDeleted = true;
            supplier.UpdatedAt = DateTime.Now;
            supplier.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> IsCodeAvailableAsync(string code, int? excludeSupplierId = null)
        {
            var query = _context.Suppliers.Where(s => s.Code == code && !s.IsDeleted);

            if (excludeSupplierId.HasValue)
                query = query.Where(s => s.Id != excludeSupplierId.Value);

            return !await query.AnyAsync();
        }

        public async Task<IEnumerable<Supplier>> GetSuppliersWithBalanceAsync()
        {
            return await _context.Suppliers
                .Where(s => !s.IsDeleted && s.CurrentBalance != 0)
                .OrderByDescending(s => s.CurrentBalance)
                .ToListAsync();
        }

        public async Task<decimal> GetSupplierBalanceAsync(int supplierId)
        {
            var supplier = await _context.Suppliers.FindAsync(supplierId);
            return supplier?.CurrentBalance ?? 0;
        }

        public async Task<bool> UpdateSupplierBalanceAsync(int supplierId, decimal amount)
        {
            var supplier = await _context.Suppliers.FindAsync(supplierId);
            if (supplier == null)
                return false;

            supplier.CurrentBalance += amount;
            supplier.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<PurchaseOrder>> GetSupplierPurchaseOrdersAsync(int supplierId)
        {
            return await _context.PurchaseOrders
                .Include(po => po.Items)
                    .ThenInclude(poi => poi.Product)
                .Where(po => po.SupplierId == supplierId && !po.IsDeleted)
                .OrderByDescending(po => po.OrderDate)
                .ToListAsync();
        }

        public async Task<string> GenerateSupplierCodeAsync()
        {
            var prefix = "SUPP";

            var lastSupplier = await _context.Suppliers
                .Where(s => s.Code!.StartsWith(prefix))
                .OrderByDescending(s => s.Code)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastSupplier != null && lastSupplier.Code != null)
            {
                var numberPart = lastSupplier.Code.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }
    }
}
