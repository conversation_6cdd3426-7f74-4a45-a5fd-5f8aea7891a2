using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class ProductsViewModel : ObservableObject
    {
        private readonly IProductService _productService;
        private readonly IUserService _userService;

        [ObservableProperty]
        private ObservableCollection<Product> products = new();

        [ObservableProperty]
        private ObservableCollection<ProductCategory> categories = new();

        [ObservableProperty]
        private Product? selectedProduct;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private Product editingProduct = new();

        public ProductsViewModel(IProductService productService, IUserService userService)
        {
            _productService = productService;
            _userService = userService;
            
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                var productsData = await _productService.GetAllProductsAsync();
                var categoriesData = await _productService.GetCategoriesAsync();
                
                Products.Clear();
                foreach (var product in productsData)
                {
                    Products.Add(product);
                }
                
                Categories.Clear();
                foreach (var category in categoriesData)
                {
                    Categories.Add(category);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void AddProduct()
        {
            EditingProduct = new Product
            {
                IsActive = true,
                MinStockLevel = 10,
                MaxStockLevel = 1000,
                Unit = "كيلو"
            };
            IsEditMode = true;
        }

        [RelayCommand]
        private void EditProduct()
        {
            if (SelectedProduct == null) return;
            
            EditingProduct = new Product
            {
                Id = SelectedProduct.Id,
                Name = SelectedProduct.Name,
                Code = SelectedProduct.Code,
                Description = SelectedProduct.Description,
                CategoryId = SelectedProduct.CategoryId,
                Type = SelectedProduct.Type,
                Quality = SelectedProduct.Quality,
                PackageSize = SelectedProduct.PackageSize,
                PurchasePrice = SelectedProduct.PurchasePrice,
                SalePrice = SelectedProduct.SalePrice,
                WholesalePrice = SelectedProduct.WholesalePrice,
                MinStockLevel = SelectedProduct.MinStockLevel,
                MaxStockLevel = SelectedProduct.MaxStockLevel,
                Unit = SelectedProduct.Unit,
                IsActive = SelectedProduct.IsActive,
                Barcode = SelectedProduct.Barcode,
                ExpiryDate = SelectedProduct.ExpiryDate
            };
            IsEditMode = true;
        }

        [RelayCommand]
        private async Task SaveProductAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(EditingProduct.Name))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (EditingProduct.CategoryId <= 0)
                {
                    MessageBox.Show("يرجى اختيار تصنيف المنتج", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                if (EditingProduct.Id == 0)
                {
                    await _productService.CreateProductAsync(EditingProduct);
                }
                else
                {
                    await _productService.UpdateProductAsync(EditingProduct);
                }

                IsEditMode = false;
                await LoadDataAsync();
                
                MessageBox.Show("تم حفظ المنتج بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void CancelEdit()
        {
            IsEditMode = false;
            EditingProduct = new Product();
        }

        [RelayCommand]
        private async Task DeleteProductAsync()
        {
            if (SelectedProduct == null) return;

            var result = MessageBox.Show($"هل تريد حذف المنتج '{SelectedProduct.Name}'؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    await _productService.DeleteProductAsync(SelectedProduct.Id);
                    await LoadDataAsync();
                    
                    MessageBox.Show("تم حذف المنتج بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;
                
                var allProducts = await _productService.GetAllProductsAsync();
                
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    Products.Clear();
                    foreach (var product in allProducts)
                    {
                        Products.Add(product);
                    }
                }
                else
                {
                    var filteredProducts = allProducts.Where(p => 
                        p.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (p.Code?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (p.Barcode?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
                    
                    Products.Clear();
                    foreach (var product in filteredProducts)
                    {
                        Products.Add(product);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ShowLowStockAsync()
        {
            try
            {
                IsLoading = true;
                
                var lowStockProducts = await _productService.GetLowStockProductsAsync();
                
                Products.Clear();
                foreach (var product in lowStockProducts)
                {
                    Products.Add(product);
                }
                
                if (!lowStockProducts.Any())
                {
                    MessageBox.Show("لا توجد منتجات بمخزون منخفض", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ManageCategories()
        {
            // TODO: Implement category management dialog
            MessageBox.Show("إدارة التصنيفات ستكون متاحة قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        partial void OnSearchTextChanged(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                _ = SearchAsync();
            }
        }
    }
}
