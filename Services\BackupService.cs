using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using DateFactoryApp.Data;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Serilog;

namespace DateFactoryApp.Services
{
    public class BackupService : IBackupService
    {
        private readonly AppDbContext _context;
        private readonly string _databasePath;
        private readonly string _backupDirectory;

        public BackupService(AppDbContext context)
        {
            _context = context;
            _databasePath = "DateFactory.db";
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "DateFactory", "Backups");

            if (!Directory.Exists(_backupDirectory))
            {
                Directory.CreateDirectory(_backupDirectory);
            }
        }

        public async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                // Ensure database is closed properly
                await _context.Database.CloseConnectionAsync();

                // Create backup directory if it doesn't exist
                var backupDir = Path.GetDirectoryName(backupPath);
                if (!string.IsNullOrEmpty(backupDir) && !Directory.Exists(backupDir))
                {
                    Directory.CreateDirectory(backupDir);
                }

                // Create a zip file containing the database and any related files
                using var archive = ZipFile.Open(backupPath, ZipArchiveMode.Create);

                // Add database file
                if (File.Exists(_databasePath))
                {
                    archive.CreateEntryFromFile(_databasePath, "DateFactory.db");
                }

                // Add any additional files (logs, images, etc.)
                var logsPath = "logs";
                if (Directory.Exists(logsPath))
                {
                    var logFiles = Directory.GetFiles(logsPath, "*.txt");
                    foreach (var logFile in logFiles)
                    {
                        var entryName = $"logs/{Path.GetFileName(logFile)}";
                        archive.CreateEntryFromFile(logFile, entryName);
                    }
                }

                // Add backup metadata
                var metadataEntry = archive.CreateEntry("backup_info.txt");
                using var metadataStream = metadataEntry.Open();
                using var writer = new StreamWriter(metadataStream);
                await writer.WriteLineAsync($"Backup Created: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                await writer.WriteLineAsync($"Database Size: {await GetDatabaseSizeAsync()} bytes");
                await writer.WriteLineAsync($"Application Version: 1.0.0");

                Log.Information("Backup created successfully at {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to create backup at {BackupPath}", backupPath);
                return false;
            }
        }

        public async Task<bool> RestoreBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    Log.Warning("Backup file not found: {BackupPath}", backupPath);
                    return false;
                }

                // Validate backup before restoring
                if (!await ValidateBackupAsync(backupPath))
                {
                    Log.Warning("Invalid backup file: {BackupPath}", backupPath);
                    return false;
                }

                // Close database connection
                await _context.Database.CloseConnectionAsync();

                // Create backup of current database before restoring
                var currentBackupPath = Path.Combine(_backupDirectory, $"pre_restore_backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip");
                await CreateBackupAsync(currentBackupPath);

                // Extract backup
                using var archive = ZipFile.OpenRead(backupPath);

                // Restore database file
                var dbEntry = archive.GetEntry("DateFactory.db");
                if (dbEntry != null)
                {
                    // Delete current database
                    if (File.Exists(_databasePath))
                    {
                        File.Delete(_databasePath);
                    }

                    // Extract new database
                    dbEntry.ExtractToFile(_databasePath);
                }

                // Restore logs if needed
                var logEntries = archive.Entries.Where(e => e.FullName.StartsWith("logs/"));
                foreach (var logEntry in logEntries)
                {
                    var logPath = logEntry.FullName;
                    var logDir = Path.GetDirectoryName(logPath);
                    if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                    {
                        Directory.CreateDirectory(logDir);
                    }
                    logEntry.ExtractToFile(logPath, true);
                }

                Log.Information("Database restored successfully from {BackupPath}", backupPath);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to restore backup from {BackupPath}", backupPath);
                return false;
            }
        }

        public async Task<bool> CreateAutoBackupAsync()
        {
            var fileName = $"auto_backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
            var backupPath = Path.Combine(_backupDirectory, fileName);

            var result = await CreateBackupAsync(backupPath);

            if (result)
            {
                // Clean up old auto backups (keep only last 10)
                await CleanupOldBackupsAsync();
            }

            return result;
        }

        public async Task<IEnumerable<BackupInfo>> GetBackupHistoryAsync()
        {
            var backups = new List<BackupInfo>();

            if (!Directory.Exists(_backupDirectory))
                return backups;

            var backupFiles = Directory.GetFiles(_backupDirectory, "*.zip");

            foreach (var file in backupFiles)
            {
                var fileInfo = new FileInfo(file);
                var backup = new BackupInfo
                {
                    FileName = fileInfo.Name,
                    FilePath = file,
                    CreatedDate = fileInfo.CreationTime,
                    FileSize = fileInfo.Length,
                    FormattedSize = FormatFileSize(fileInfo.Length),
                    IsValid = await ValidateBackupAsync(file)
                };

                backups.Add(backup);
            }

            return backups.OrderByDescending(b => b.CreatedDate);
        }

        public async Task<bool> DeleteBackupAsync(string backupPath)
        {
            try
            {
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                    Log.Information("Backup deleted: {BackupPath}", backupPath);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to delete backup: {BackupPath}", backupPath);
                return false;
            }
        }

        public async Task<long> GetDatabaseSizeAsync()
        {
            try
            {
                if (File.Exists(_databasePath))
                {
                    var fileInfo = new FileInfo(_databasePath);
                    return fileInfo.Length;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        public async Task<bool> ValidateBackupAsync(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                    return false;

                using var archive = ZipFile.OpenRead(backupPath);

                // Check if database file exists in backup
                var dbEntry = archive.GetEntry("DateFactory.db");
                if (dbEntry == null)
                    return false;

                // Check if backup info exists
                var infoEntry = archive.GetEntry("backup_info.txt");
                if (infoEntry == null)
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        public string GetDefaultBackupPath()
        {
            var fileName = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip";
            return Path.Combine(_backupDirectory, fileName);
        }

        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                var backups = await GetBackupHistoryAsync();
                var autoBackups = backups.Where(b => b.FileName.StartsWith("auto_backup_")).ToList();

                if (autoBackups.Count > 10)
                {
                    var oldBackups = autoBackups.OrderByDescending(b => b.CreatedDate).Skip(10);
                    foreach (var backup in oldBackups)
                    {
                        await DeleteBackupAsync(backup.FilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to cleanup old backups");
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
