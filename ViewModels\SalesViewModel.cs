using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class SalesViewModel : ObservableObject
    {
        private readonly ISalesService _salesService;
        private readonly IProductService _productService;
        private readonly ICustomerService _customerService;
        private readonly IUserService _userService;

        [ObservableProperty]
        private ObservableCollection<Sale> sales = new();

        [ObservableProperty]
        private ObservableCollection<Product> products = new();

        [ObservableProperty]
        private ObservableCollection<Customer> customers = new();

        [ObservableProperty]
        private Sale? selectedSale;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isNewSaleMode = false;

        [ObservableProperty]
        private Sale currentSale = new();

        [ObservableProperty]
        private ObservableCollection<SaleItem> currentSaleItems = new();

        [ObservableProperty]
        private Product? selectedProduct;

        [ObservableProperty]
        private decimal quantity = 1;

        [ObservableProperty]
        private decimal unitPrice = 0;

        [ObservableProperty]
        private DateTime startDate = DateTime.Today.AddDays(-30);

        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        public SalesViewModel(ISalesService salesService, IProductService productService, 
            ICustomerService customerService, IUserService userService)
        {
            _salesService = salesService;
            _productService = productService;
            _customerService = customerService;
            _userService = userService;
            
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                var salesData = await _salesService.GetSalesByDateRangeAsync(StartDate, EndDate);
                var productsData = await _productService.GetActiveProductsAsync();
                var customersData = await _customerService.GetActiveCustomersAsync();
                
                Sales.Clear();
                foreach (var sale in salesData)
                {
                    Sales.Add(sale);
                }
                
                Products.Clear();
                foreach (var product in productsData)
                {
                    Products.Add(product);
                }
                
                Customers.Clear();
                foreach (var customer in customersData)
                {
                    Customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task StartNewSaleAsync()
        {
            try
            {
                var invoiceNumber = await _salesService.GenerateInvoiceNumberAsync();
                
                CurrentSale = new Sale
                {
                    InvoiceNumber = invoiceNumber,
                    SaleDate = DateTime.Now,
                    PaymentMethod = PaymentMethod.Cash,
                    Status = SaleStatus.Draft
                };
                
                CurrentSaleItems.Clear();
                IsNewSaleMode = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private void AddProductToSale()
        {
            if (SelectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (Quantity <= 0)
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (UnitPrice <= 0)
            {
                UnitPrice = SelectedProduct.SalePrice;
            }

            // Check if product already exists in sale
            var existingItem = CurrentSaleItems.FirstOrDefault(i => i.ProductId == SelectedProduct.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += Quantity;
                existingItem.TotalPrice = existingItem.Quantity * existingItem.UnitPrice;
            }
            else
            {
                var saleItem = new SaleItem
                {
                    ProductId = SelectedProduct.Id,
                    Product = SelectedProduct,
                    Quantity = Quantity,
                    UnitPrice = UnitPrice,
                    TotalPrice = Quantity * UnitPrice
                };
                
                CurrentSaleItems.Add(saleItem);
            }

            // Reset input fields
            SelectedProduct = null;
            Quantity = 1;
            UnitPrice = 0;
            
            CalculateSaleTotals();
        }

        [RelayCommand]
        private void RemoveProductFromSale(SaleItem item)
        {
            CurrentSaleItems.Remove(item);
            CalculateSaleTotals();
        }

        [RelayCommand]
        private async Task SaveSaleAsync()
        {
            try
            {
                if (CurrentSale.CustomerId <= 0)
                {
                    MessageBox.Show("يرجى اختيار عميل", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!CurrentSaleItems.Any())
                {
                    MessageBox.Show("يرجى إضافة منتجات للفاتورة", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                CurrentSale.SaleItems = CurrentSaleItems.ToList();
                CurrentSale.Status = SaleStatus.Completed;

                await _salesService.CreateSaleAsync(CurrentSale);
                
                IsNewSaleMode = false;
                await LoadDataAsync();
                
                MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void CancelSale()
        {
            var result = MessageBox.Show("هل تريد إلغاء الفاتورة الحالية؟", 
                "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                IsNewSaleMode = false;
                CurrentSale = new Sale();
                CurrentSaleItems.Clear();
            }
        }

        [RelayCommand]
        private async Task DeleteSaleAsync()
        {
            if (SelectedSale == null) return;

            var result = MessageBox.Show($"هل تريد حذف الفاتورة رقم '{SelectedSale.InvoiceNumber}'؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    await _salesService.DeleteSaleAsync(SelectedSale.Id);
                    await LoadDataAsync();
                    
                    MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private void PrintSale()
        {
            if (SelectedSale == null) return;
            
            // TODO: Implement print functionality
            MessageBox.Show("الطباعة ستكون متاحة قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task FilterSalesAsync()
        {
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task ShowTodaySalesAsync()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task ShowThisWeekSalesAsync()
        {
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            StartDate = startOfWeek;
            EndDate = DateTime.Today;
            await LoadDataAsync();
        }

        [RelayCommand]
        private async Task ShowThisMonthSalesAsync()
        {
            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            EndDate = DateTime.Today;
            await LoadDataAsync();
        }

        partial void OnSelectedProductChanged(Product? value)
        {
            if (value != null)
            {
                UnitPrice = value.SalePrice;
            }
        }

        private void CalculateSaleTotals()
        {
            CurrentSale.SubTotal = CurrentSaleItems.Sum(item => item.TotalPrice);
            CurrentSale.TotalAmount = CurrentSale.SubTotal + CurrentSale.TaxAmount - CurrentSale.DiscountAmount;
            CurrentSale.RemainingAmount = CurrentSale.TotalAmount - CurrentSale.PaidAmount;
        }
    }
}
