using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class SuppliersViewModel : ObservableObject
    {
        private readonly ISupplierService _supplierService;

        [ObservableProperty]
        private ObservableCollection<Supplier> suppliers = new();

        [ObservableProperty]
        private Supplier? selectedSupplier;

        [ObservableProperty]
        private string searchText = string.Empty;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isEditMode = false;

        [ObservableProperty]
        private Supplier editingSupplier = new();

        public SuppliersViewModel(ISupplierService supplierService)
        {
            _supplierService = supplierService;
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                var suppliersData = await _supplierService.GetAllSuppliersAsync();
                
                Suppliers.Clear();
                foreach (var supplier in suppliersData)
                {
                    Suppliers.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void AddSupplier()
        {
            EditingSupplier = new Supplier
            {
                IsActive = true,
                Type = SupplierType.DateFarmer,
                PaymentTerms = 30
            };
            IsEditMode = true;
        }

        [RelayCommand]
        private void EditSupplier()
        {
            if (SelectedSupplier == null) return;
            
            EditingSupplier = new Supplier
            {
                Id = SelectedSupplier.Id,
                Name = SelectedSupplier.Name,
                Code = SelectedSupplier.Code,
                Phone = SelectedSupplier.Phone,
                Email = SelectedSupplier.Email,
                Address = SelectedSupplier.Address,
                City = SelectedSupplier.City,
                Region = SelectedSupplier.Region,
                ContactPerson = SelectedSupplier.ContactPerson,
                ContactPhone = SelectedSupplier.ContactPhone,
                Type = SelectedSupplier.Type,
                IsActive = SelectedSupplier.IsActive,
                Notes = SelectedSupplier.Notes,
                PaymentTerms = SelectedSupplier.PaymentTerms
            };
            IsEditMode = true;
        }

        [RelayCommand]
        private async Task SaveSupplierAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(EditingSupplier.Name))
                {
                    MessageBox.Show("يرجى إدخال اسم المورد", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                if (EditingSupplier.Id == 0)
                {
                    await _supplierService.CreateSupplierAsync(EditingSupplier);
                }
                else
                {
                    await _supplierService.UpdateSupplierAsync(EditingSupplier);
                }

                IsEditMode = false;
                await LoadDataAsync();
                
                MessageBox.Show("تم حفظ المورد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void CancelEdit()
        {
            IsEditMode = false;
            EditingSupplier = new Supplier();
        }

        [RelayCommand]
        private async Task DeleteSupplierAsync()
        {
            if (SelectedSupplier == null) return;

            var result = MessageBox.Show($"هل تريد حذف المورد '{SelectedSupplier.Name}'؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    IsLoading = true;
                    await _supplierService.DeleteSupplierAsync(SelectedSupplier.Id);
                    await LoadDataAsync();
                    
                    MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    IsLoading = false;
                }
            }
        }

        [RelayCommand]
        private async Task SearchAsync()
        {
            try
            {
                IsLoading = true;
                
                var allSuppliers = await _supplierService.GetAllSuppliersAsync();
                
                if (string.IsNullOrWhiteSpace(SearchText))
                {
                    Suppliers.Clear();
                    foreach (var supplier in allSuppliers)
                    {
                        Suppliers.Add(supplier);
                    }
                }
                else
                {
                    var filteredSuppliers = allSuppliers.Where(s => 
                        s.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        (s.Code?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                        (s.Phone?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
                    
                    Suppliers.Clear();
                    foreach (var supplier in filteredSuppliers)
                    {
                        Suppliers.Add(supplier);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ShowSuppliersWithBalanceAsync()
        {
            try
            {
                IsLoading = true;
                
                var suppliersWithBalance = await _supplierService.GetSuppliersWithBalanceAsync();
                
                Suppliers.Clear();
                foreach (var supplier in suppliersWithBalance)
                {
                    Suppliers.Add(supplier);
                }
                
                if (!suppliersWithBalance.Any())
                {
                    MessageBox.Show("لا توجد موردين برصيد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ViewSupplierOrdersAsync()
        {
            if (SelectedSupplier == null) return;

            try
            {
                IsLoading = true;
                var orders = await _supplierService.GetSupplierPurchaseOrdersAsync(SelectedSupplier.Id);
                
                if (orders.Any())
                {
                    var message = $"أوامر شراء المورد '{SelectedSupplier.Name}':\n\n";
                    var totalAmount = 0m;
                    
                    foreach (var order in orders.Take(10))
                    {
                        message += $"• أمر {order.OrderNumber} - {order.OrderDate:yyyy/MM/dd} - {order.TotalAmount:C}\n";
                        totalAmount += order.TotalAmount;
                    }
                    
                    if (orders.Count() > 10)
                    {
                        message += $"\n... و {orders.Count() - 10} أمر آخر";
                    }
                    
                    message += $"\n\nإجمالي المشتريات: {totalAmount:C}";
                    
                    MessageBox.Show(message, "أوامر شراء المورد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا توجد أوامر شراء لهذا المورد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أوامر المورد: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ExportSuppliers()
        {
            // TODO: Implement export functionality
            MessageBox.Show("تصدير الموردين سيكون متاح قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        partial void OnSearchTextChanged(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                _ = SearchAsync();
            }
        }
    }
}
