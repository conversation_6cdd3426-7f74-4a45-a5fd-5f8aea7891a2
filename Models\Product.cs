using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class Product : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(50)]
        public string? Code { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        public int CategoryId { get; set; }

        [Required]
        public ProductType Type { get; set; }

        [Required]
        public DateQuality Quality { get; set; }

        [Required]
        public PackageSize PackageSize { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchasePrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal WholesalePrice { get; set; }

        public int MinStockLevel { get; set; } = 10;

        public int MaxStockLevel { get; set; } = 1000;

        public int CurrentStock { get; set; } = 0;

        [MaxLength(50)]
        public string? Unit { get; set; } = "كيلو";

        public bool IsActive { get; set; } = true;

        [MaxLength(255)]
        public string? ImagePath { get; set; }

        [MaxLength(100)]
        public string? Barcode { get; set; }

        public DateTime? ExpiryDate { get; set; }

        // Navigation properties
        public virtual ProductCategory Category { get; set; } = null!;
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
        public virtual ICollection<ProductionRecord> ProductionRecords { get; set; } = new List<ProductionRecord>();
    }

    public enum ProductType
    {
        RawDates = 1,      // تمر خام
        ProcessedDates = 2, // تمر معالج
        PackagedDates = 3,  // تمر معبأ
        DatePaste = 4,      // معجون تمر
        DateSyrup = 5,      // دبس تمر
        DatePowder = 6      // مسحوق تمر
    }

    public enum DateQuality
    {
        Premium = 1,    // ممتاز
        Good = 2,       // جيد
        Standard = 3,   // عادي
        Economy = 4     // اقتصادي
    }

    public enum PackageSize
    {
        Bulk = 1,       // بالجملة
        Kg25 = 2,       // 25 كيلو
        Kg10 = 3,       // 10 كيلو
        Kg5 = 4,        // 5 كيلو
        Kg1 = 5,        // 1 كيلو
        Gram500 = 6,    // 500 جرام
        Gram250 = 7     // 250 جرام
    }
}
