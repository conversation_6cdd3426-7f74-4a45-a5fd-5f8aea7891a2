@echo off
echo ========================================
echo    نظام إدارة مصنع ومحل التمور
echo ========================================
echo.

echo جاري التحقق من متطلبات التشغيل...

:: Check if .NET 8 is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: .NET 8.0 غير مثبت على النظام
    echo يرجى تحميل وتثبيت .NET 8.0 Runtime من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo تم العثور على .NET Runtime
echo.

echo جاري استعادة الحزم المطلوبة...
dotnet restore
if %errorlevel% neq 0 (
    echo خطأ في استعادة الحزم
    pause
    exit /b 1
)

echo جاري بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo خطأ في بناء المشروع
    pause
    exit /b 1
)

echo.
echo ========================================
echo       تشغيل التطبيق...
echo ========================================
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.

:: Create necessary directories
if not exist "logs" mkdir logs
if not exist "Backups" mkdir Backups
if not exist "Reports" mkdir Reports

:: Run the application
dotnet run --configuration Release

echo.
echo تم إغلاق التطبيق
pause
