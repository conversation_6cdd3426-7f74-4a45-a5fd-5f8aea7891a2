using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface IInventoryService
    {
        Task<IEnumerable<InventoryTransaction>> GetAllTransactionsAsync();
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId);
        Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<InventoryTransaction?> GetTransactionByIdAsync(int id);
        Task<InventoryTransaction> CreateTransactionAsync(InventoryTransaction transaction);
        Task<InventoryTransaction> UpdateTransactionAsync(InventoryTransaction transaction);
        Task<bool> DeleteTransactionAsync(int id);
        Task<string> GenerateTransactionNumberAsync(TransactionType type);
        Task<decimal> GetCurrentStockAsync(int productId);
        Task<IEnumerable<Product>> GetLowStockProductsAsync();
        Task<IEnumerable<Product>> GetOutOfStockProductsAsync();
        Task<bool> AdjustStockAsync(int productId, decimal newQuantity, string reason);
    }
}
