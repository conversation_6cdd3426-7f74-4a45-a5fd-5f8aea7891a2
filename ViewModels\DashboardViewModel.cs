using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using LiveCharts;
using LiveCharts.Configurations;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class DashboardViewModel : ObservableObject
    {
        private readonly ISalesService _salesService;
        private readonly IProductService _productService;
        private readonly ICustomerService _customerService;
        private readonly IInventoryService _inventoryService;
        private readonly IBackupService _backupService;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private decimal totalSales = 0;

        [ObservableProperty]
        private int totalProducts = 0;

        [ObservableProperty]
        private int lowStockCount = 0;

        [ObservableProperty]
        private int totalCustomers = 0;

        [ObservableProperty]
        private ObservableCollection<Sale> recentSales = new();

        [ObservableProperty]
        private ObservableCollection<Product> lowStockProducts = new();

        [ObservableProperty]
        private ChartValues<decimal> salesChartData = new();

        [ObservableProperty]
        private List<string> salesChartLabels = new();

        public Func<double, string> CurrencyFormatter { get; set; }

        public DashboardViewModel(ISalesService salesService, IProductService productService,
            ICustomerService customerService, IInventoryService inventoryService, IBackupService backupService)
        {
            _salesService = salesService;
            _productService = productService;
            _customerService = customerService;
            _inventoryService = inventoryService;
            _backupService = backupService;

            CurrencyFormatter = value => value.ToString("C");

            LoadDashboardDataAsync();
        }

        [RelayCommand]
        private async Task LoadDashboardDataAsync()
        {
            try
            {
                IsLoading = true;

                // Load statistics
                await LoadStatisticsAsync();

                // Load recent sales
                await LoadRecentSalesAsync();

                // Load low stock products
                await LoadLowStockProductsAsync();

                // Load sales chart data
                await LoadSalesChartDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadStatisticsAsync()
        {
            // Total sales for current month
            var startOfMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            TotalSales = await _salesService.GetTotalSalesAsync(startOfMonth, DateTime.Today);

            // Total products
            var products = await _productService.GetAllProductsAsync();
            TotalProducts = products.Count();

            // Low stock count
            var lowStockProducts = await _inventoryService.GetLowStockProductsAsync();
            LowStockCount = lowStockProducts.Count();

            // Total customers
            var customers = await _customerService.GetAllCustomersAsync();
            TotalCustomers = customers.Count();
        }

        private async Task LoadRecentSalesAsync()
        {
            var sales = await _salesService.GetSalesByDateRangeAsync(DateTime.Today.AddDays(-7), DateTime.Today);
            
            RecentSales.Clear();
            foreach (var sale in sales.Take(5).OrderByDescending(s => s.SaleDate))
            {
                RecentSales.Add(sale);
            }
        }

        private async Task LoadLowStockProductsAsync()
        {
            var products = await _inventoryService.GetLowStockProductsAsync();
            
            LowStockProducts.Clear();
            foreach (var product in products.Take(5))
            {
                LowStockProducts.Add(product);
            }
        }

        private async Task LoadSalesChartDataAsync()
        {
            var chartData = new ChartValues<decimal>();
            var labels = new List<string>();

            for (int i = 6; i >= 0; i--)
            {
                var date = DateTime.Today.AddDays(-i);
                var dailySales = await _salesService.GetTotalSalesAsync(date, date);
                
                chartData.Add(dailySales);
                labels.Add(date.ToString("MM/dd"));
            }

            SalesChartData = chartData;
            SalesChartLabels = labels;
        }

        [RelayCommand]
        private void NewSale()
        {
            // Navigate to sales page with new sale
            // This would be handled by the main view model
            MessageBox.Show("سيتم فتح صفحة المبيعات لإنشاء فاتورة جديدة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void NewProduct()
        {
            // Navigate to products page with new product
            MessageBox.Show("سيتم فتح صفحة المنتجات لإضافة منتج جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void NewCustomer()
        {
            // Navigate to customers page with new customer
            MessageBox.Show("سيتم فتح صفحة العملاء لإضافة عميل جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private async Task CreateBackupAsync()
        {
            try
            {
                IsLoading = true;
                
                var backupPath = _backupService.GetDefaultBackupPath();
                var success = await _backupService.CreateBackupAsync(backupPath);
                
                if (success)
                {
                    MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إنشاء النسخة الاحتياطية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task RefreshDataAsync()
        {
            await LoadDashboardDataAsync();
        }
    }
}
