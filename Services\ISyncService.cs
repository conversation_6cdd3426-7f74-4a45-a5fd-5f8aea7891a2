namespace DateFactoryApp.Services
{
    public interface ISyncService
    {
        Task<bool> SyncToCloudAsync();
        Task<bool> SyncFromCloudAsync();
        Task<bool> IsOnlineAsync();
        Task<SyncStatus> GetSyncStatusAsync();
        Task<bool> EnableAutoSyncAsync(bool enable);
        Task<bool> IsAutoSyncEnabledAsync();
        Task<DateTime?> GetLastSyncTimeAsync();
        Task<int> GetPendingSyncCountAsync();
        Task<bool> ConfigureSyncEndpointAsync(string endpoint, string apiKey);
        event EventHandler<SyncProgressEventArgs>? SyncProgressChanged;
    }

    public class SyncStatus
    {
        public bool IsOnline { get; set; }
        public DateTime? LastSyncTime { get; set; }
        public int PendingChanges { get; set; }
        public bool AutoSyncEnabled { get; set; }
        public string? LastError { get; set; }
        public SyncState State { get; set; }
    }

    public enum SyncState
    {
        Idle,
        Syncing,
        Error,
        Offline
    }

    public class SyncProgressEventArgs : EventArgs
    {
        public int TotalItems { get; set; }
        public int ProcessedItems { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public double ProgressPercentage => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
    }
}
