using System;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Services;
using MaterialDesignThemes.Wpf;

namespace DateFactoryApp.ViewModels
{
    public partial class SettingsViewModel : ObservableObject
    {
        private readonly IThemeService _themeService;
        private readonly IBackupService _backupService;
        private readonly ISyncService _syncService;
        private readonly IUserService _userService;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isDarkTheme;

        [ObservableProperty]
        private string selectedPrimaryColor = "Blue";

        [ObservableProperty]
        private string selectedSecondaryColor = "Lime";

        [ObservableProperty]
        private bool autoSyncEnabled;

        [ObservableProperty]
        private string syncEndpoint = string.Empty;

        [ObservableProperty]
        private string syncApiKey = string.Empty;

        [ObservableProperty]
        private string backupPath = string.Empty;

        [ObservableProperty]
        private bool autoBackupEnabled = false;

        [ObservableProperty]
        private string currentUserName = string.Empty;

        [ObservableProperty]
        private string newPassword = string.Empty;

        [ObservableProperty]
        private string confirmPassword = string.Empty;

        [ObservableProperty]
        private string oldPassword = string.Empty;

        public SettingsViewModel(IThemeService themeService, IBackupService backupService,
            ISyncService syncService, IUserService userService)
        {
            _themeService = themeService;
            _backupService = backupService;
            _syncService = syncService;
            _userService = userService;

            LoadSettingsAsync();
        }

        private async void LoadSettingsAsync()
        {
            try
            {
                IsLoading = true;

                // Load theme settings
                IsDarkTheme = _themeService.IsDarkTheme;
                SelectedPrimaryColor = _themeService.GetCurrentPrimaryColor();
                SelectedSecondaryColor = _themeService.GetCurrentSecondaryColor();

                // Load sync settings
                AutoSyncEnabled = await _syncService.IsAutoSyncEnabledAsync();

                // Load backup settings
                BackupPath = _backupService.GetDefaultBackupPath();

                // Load user info
                var currentUser = await _userService.GetCurrentUserAsync();
                CurrentUserName = currentUser?.FullName ?? "";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ApplyThemeSettings()
        {
            try
            {
                _themeService.SetTheme(IsDarkTheme);
                _themeService.SetPrimaryColor(SelectedPrimaryColor);
                _themeService.SetSecondaryColor(SelectedSecondaryColor);

                MessageBox.Show("تم تطبيق إعدادات الثيم بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق إعدادات الثيم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        [RelayCommand]
        private async Task ApplySyncSettingsAsync()
        {
            try
            {
                IsLoading = true;

                await _syncService.EnableAutoSyncAsync(AutoSyncEnabled);

                if (!string.IsNullOrEmpty(SyncEndpoint) && !string.IsNullOrEmpty(SyncApiKey))
                {
                    await _syncService.ConfigureSyncEndpointAsync(SyncEndpoint, SyncApiKey);
                }

                MessageBox.Show("تم حفظ إعدادات المزامنة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ إعدادات المزامنة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task TestSyncConnectionAsync()
        {
            try
            {
                IsLoading = true;

                var isOnline = await _syncService.IsOnlineAsync();

                if (isOnline)
                {
                    MessageBox.Show("الاتصال بالإنترنت متاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا يوجد اتصال بالإنترنت", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختبار الاتصال: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CreateBackupAsync()
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Backup Files (*.zip)|*.zip",
                    DefaultExt = "zip",
                    FileName = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}.zip"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    IsLoading = true;

                    var success = await _backupService.CreateBackupAsync(saveDialog.FileName);

                    if (success)
                    {
                        MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء النسخة الاحتياطية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task RestoreBackupAsync()
        {
            try
            {
                var openDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Filter = "Backup Files (*.zip)|*.zip",
                    DefaultExt = "zip"
                };

                if (openDialog.ShowDialog() == true)
                {
                    var result = MessageBox.Show(
                        "هل تريد استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.",
                        "تأكيد الاستعادة",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        IsLoading = true;

                        var success = await _backupService.RestoreBackupAsync(openDialog.FileName);

                        if (success)
                        {
                            MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق.",
                                "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في استعادة النسخة الاحتياطية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void BrowseBackupPath()
        {
            var folderDialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "اختر مجلد النسخ الاحتياطية",
                ShowNewFolderButton = true
            };

            if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                BackupPath = folderDialog.SelectedPath;
            }
        }

        [RelayCommand]
        private async Task ChangePasswordAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(OldPassword))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور الحالية", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(NewPassword))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور الجديدة", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (NewPassword != ConfirmPassword)
                {
                    MessageBox.Show("كلمة المرور الجديدة وتأكيدها غير متطابقين", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (NewPassword.Length < 6)
                {
                    MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser != null)
                {
                    var success = await _userService.ChangePasswordAsync(currentUser.Id, OldPassword, NewPassword);

                    if (success)
                    {
                        OldPassword = string.Empty;
                        NewPassword = string.Empty;
                        ConfirmPassword = string.Empty;

                        MessageBox.Show("تم تغيير كلمة المرور بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("كلمة المرور الحالية غير صحيحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير كلمة المرور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ResetToDefaults()
        {
            var result = MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                IsDarkTheme = false;
                SelectedPrimaryColor = PrimaryColor.Brown;
                SelectedSecondaryColor = SecondaryColor.Orange;
                AutoSyncEnabled = false;
                SyncEndpoint = string.Empty;
                SyncApiKey = string.Empty;

                ApplyThemeSettings();

                MessageBox.Show("تم إعادة تعيين الإعدادات بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
