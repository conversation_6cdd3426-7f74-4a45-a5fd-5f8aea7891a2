using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class ProductionRecord : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string BatchNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime ProductionDate { get; set; } = DateTime.Now;
        
        [Required]
        public int ProductId { get; set; }
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal ProducedQuantity { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal ProductionCost { get; set; }
        
        [Required]
        public ProductionStatus Status { get; set; } = ProductionStatus.InProgress;
        
        [MaxLength(500)]
        public string? Notes { get; set; }
        
        public int? SupervisorId { get; set; }
        
        public DateTime? StartTime { get; set; }
        
        public DateTime? EndTime { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal WastePercentage { get; set; } = 0;
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal WasteQuantity { get; set; } = 0;
        
        // Navigation properties
        public virtual Product Product { get; set; } = null!;
        public virtual User? Supervisor { get; set; }
        public virtual ICollection<ProductionMaterial> Materials { get; set; } = new List<ProductionMaterial>();
        public virtual ICollection<InventoryTransaction> InventoryTransactions { get; set; } = new List<InventoryTransaction>();
    }

    public class ProductionMaterial : BaseEntity
    {
        [Required]
        public int ProductionRecordId { get; set; }
        
        [Required]
        public int MaterialId { get; set; }
        
        [Column(TypeName = "decimal(18,3)")]
        public decimal UsedQuantity { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitCost { get; set; }
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalCost { get; set; }
        
        // Navigation properties
        public virtual ProductionRecord ProductionRecord { get; set; } = null!;
        public virtual Product Material { get; set; } = null!;
    }

    public enum ProductionStatus
    {
        Planned = 1,        // مخطط
        InProgress = 2,     // قيد التنفيذ
        Completed = 3,      // مكتمل
        OnHold = 4,         // متوقف
        Cancelled = 5       // ملغي
    }
}
