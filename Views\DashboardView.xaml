<UserControl x:Class="DateFactoryApp.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
             FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Page Title -->
            <TextBlock Grid.Row="0" Text="لوحة التحكم" Style="{StaticResource PageTitle}"/>

            <!-- Statistics Cards -->
            <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,16">
                <!-- Total Sales Card -->
                <materialDesign:Card Style="{StaticResource DashboardCard}" Background="{StaticResource PrimaryBrush}" Cursor="Hand">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="CashRegister" Style="{StaticResource StatIcon}" Foreground="White"/>
                        <TextBlock Text="{Binding TotalSales, StringFormat='{}{0:C}'}"
                                 Style="{StaticResource StatNumber}" Foreground="White"/>
                        <TextBlock Text="إجمالي المبيعات" Style="{StaticResource StatLabel}" Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Products Card -->
                <materialDesign:Card Style="{StaticResource DashboardCard}" Background="{StaticResource SecondaryBrush}" Cursor="Hand">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="Package" Style="{StaticResource StatIcon}" Foreground="White"/>
                        <TextBlock Text="{Binding TotalProducts}"
                                 Style="{StaticResource StatNumber}" Foreground="White"/>
                        <TextBlock Text="إجمالي المنتجات" Style="{StaticResource StatLabel}" Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Low Stock Card -->
                <materialDesign:Card Style="{StaticResource DashboardCard}" Background="{StaticResource WarningBrush}" Cursor="Hand">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="AlertCircle" Style="{StaticResource StatIcon}" Foreground="White"/>
                        <TextBlock Text="{Binding LowStockCount}"
                                 Style="{StaticResource StatNumber}" Foreground="White"/>
                        <TextBlock Text="مخزون منخفض" Style="{StaticResource StatLabel}" Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Total Customers Card -->
                <materialDesign:Card Style="{StaticResource DashboardCard}" Background="{StaticResource SuccessBrush}" Cursor="Hand">
                    <StackPanel>
                        <materialDesign:PackIcon Kind="AccountGroup" Style="{StaticResource StatIcon}" Foreground="White"/>
                        <TextBlock Text="{Binding TotalCustomers}"
                                 Style="{StaticResource StatNumber}" Foreground="White"/>
                        <TextBlock Text="إجمالي العملاء" Style="{StaticResource StatLabel}" Foreground="White"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource DashboardCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="الإجراءات السريعة" Style="{StaticResource SectionTitle}"/>
                    <UniformGrid Columns="4" Margin="0,8">
                        <Button Content="فاتورة جديدة" Style="{StaticResource PrimaryButton}"
                              Command="{Binding NewSaleCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="منتج جديد" Style="{StaticResource SecondaryButton}"
                              Command="{Binding NewProductCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="PackagePlus" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="عميل جديد" Style="{StaticResource SecondaryButton}"
                              Command="{Binding NewCustomerCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="نسخة احتياطية" Style="{StaticResource WarningButton}"
                              Command="{Binding CreateBackupCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Backup" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Charts and Recent Data -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sales Chart -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource DashboardCard}" Margin="0,0,8,0">
                    <StackPanel>
                        <TextBlock Text="مبيعات آخر 7 أيام" Style="{StaticResource SectionTitle}"/>
                        <lvc:CartesianChart Height="300" Margin="0,8">
                            <lvc:CartesianChart.Series>
                                <lvc:LineSeries Values="{Binding SalesChartData}"
                                              Title="المبيعات"
                                              Stroke="{StaticResource PrimaryBrush}"
                                              Fill="Transparent"
                                              StrokeThickness="3"
                                              PointGeometry="{x:Static lvc:DefaultGeometries.Circle}"
                                              PointGeometrySize="8"/>
                            </lvc:CartesianChart.Series>
                            <lvc:CartesianChart.AxisX>
                                <lvc:Axis Title="التاريخ" Labels="{Binding SalesChartLabels}"/>
                            </lvc:CartesianChart.AxisX>
                            <lvc:CartesianChart.AxisY>
                                <lvc:Axis Title="المبلغ" LabelFormatter="{Binding CurrencyFormatter}"/>
                            </lvc:CartesianChart.AxisY>
                        </lvc:CartesianChart>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Recent Activities and Alerts -->
                <StackPanel Grid.Column="1">
                    <!-- Recent Sales -->
                    <materialDesign:Card Style="{StaticResource DashboardCard}" Margin="8,0,0,8">
                        <StackPanel>
                            <TextBlock Text="آخر المبيعات" Style="{StaticResource SectionTitle}"/>
                            <ListBox ItemsSource="{Binding RecentSales}" MaxHeight="200">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="0,4">
                                            <TextBlock Text="{Binding InvoiceNumber}" FontWeight="Medium"/>
                                            <TextBlock Text="{Binding Customer.Name}"
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                            <TextBlock Text="{Binding TotalAmount, StringFormat='{}{0:C}'}"
                                                     Foreground="{StaticResource PrimaryBrush}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Low Stock Alerts -->
                    <materialDesign:Card Style="{StaticResource DashboardCard}" Margin="8,0,0,0">
                        <StackPanel>
                            <TextBlock Text="تنبيهات المخزون" Style="{StaticResource SectionTitle}"/>
                            <ListBox ItemsSource="{Binding LowStockProducts}" MaxHeight="200">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Margin="0,4">
                                            <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                            <StackPanel Orientation="Horizontal">
                                                <TextBlock Text="الكمية: "
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                <TextBlock Text="{Binding CurrentStock}"
                                                         Foreground="{StaticResource ErrorBrush}"/>
                                                <TextBlock Text="{Binding Unit}" Margin="4,0,0,0"
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
