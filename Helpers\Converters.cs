using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DateFactoryApp.Helpers
{
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }
            return false;
        }
    }

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Collapsed;
            }
            return true;
        }
    }

    public class ProductFormTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int id)
            {
                return id == 0 ? "منتج جديد" : "تعديل المنتج";
            }
            return "منتج جديد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class CustomerFormTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int id)
            {
                return id == 0 ? "عميل جديد" : "تعديل العميل";
            }
            return "عميل جديد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class SupplierFormTitleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int id)
            {
                return id == 0 ? "مورد جديد" : "تعديل المورد";
            }
            return "مورد جديد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class EnumToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return string.Empty;
            
            return value switch
            {
                Models.UserRole.Manager => "مدير",
                Models.UserRole.Supervisor => "مشرف",
                Models.UserRole.Cashier => "كاشير",
                Models.UserRole.Worker => "عامل",
                
                Models.ProductType.RawDates => "تمر خام",
                Models.ProductType.ProcessedDates => "تمر معالج",
                Models.ProductType.PackagedDates => "تمر معبأ",
                Models.ProductType.DatePaste => "معجون تمر",
                Models.ProductType.DateSyrup => "دبس تمر",
                Models.ProductType.DatePowder => "مسحوق تمر",
                
                Models.DateQuality.Premium => "ممتاز",
                Models.DateQuality.Good => "جيد",
                Models.DateQuality.Standard => "عادي",
                Models.DateQuality.Economy => "اقتصادي",
                
                Models.PackageSize.Bulk => "بالجملة",
                Models.PackageSize.Kg25 => "25 كيلو",
                Models.PackageSize.Kg10 => "10 كيلو",
                Models.PackageSize.Kg5 => "5 كيلو",
                Models.PackageSize.Kg1 => "1 كيلو",
                Models.PackageSize.Gram500 => "500 جرام",
                Models.PackageSize.Gram250 => "250 جرام",
                
                Models.CustomerType.Retail => "تجزئة",
                Models.CustomerType.Wholesale => "جملة",
                Models.CustomerType.Distributor => "موزع",
                Models.CustomerType.Export => "تصدير",
                
                Models.SupplierType.DateFarmer => "مزارع تمور",
                Models.SupplierType.Processor => "معالج",
                Models.SupplierType.Packaging => "تعبئة وتغليف",
                Models.SupplierType.Equipment => "معدات",
                Models.SupplierType.Services => "خدمات",
                
                Models.PaymentMethod.Cash => "نقدي",
                Models.PaymentMethod.Credit => "آجل",
                Models.PaymentMethod.Card => "بطاقة",
                Models.PaymentMethod.Transfer => "تحويل",
                Models.PaymentMethod.Check => "شيك",
                
                Models.SaleStatus.Draft => "مسودة",
                Models.SaleStatus.Completed => "مكتملة",
                Models.SaleStatus.Cancelled => "ملغية",
                Models.SaleStatus.Returned => "مرتجعة",
                
                Models.TransactionType.Purchase => "شراء",
                Models.TransactionType.Sale => "بيع",
                Models.TransactionType.Production => "إنتاج",
                Models.TransactionType.Adjustment => "تسوية",
                Models.TransactionType.Transfer => "نقل",
                Models.TransactionType.Return => "إرجاع",
                Models.TransactionType.Damage => "تالف",
                Models.TransactionType.Expired => "منتهي الصلاحية",
                
                _ => value.ToString() ?? string.Empty
            };
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class CurrencyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue.ToString("C", new CultureInfo("ar-SA"));
            }
            return "0.00 ر.س";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && decimal.TryParse(stringValue.Replace("ر.س", "").Trim(), out decimal result))
            {
                return result;
            }
            return 0m;
        }
    }

    public class DateConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                return dateTime.ToString("yyyy/MM/dd", new CultureInfo("ar-SA"));
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue && DateTime.TryParse(stringValue, out DateTime result))
            {
                return result;
            }
            return DateTime.Now;
        }
    }

    public class StockLevelToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Models.Product product)
            {
                if (product.CurrentStock <= 0)
                    return Application.Current.Resources["ErrorBrush"];
                else if (product.CurrentStock <= product.MinStockLevel)
                    return Application.Current.Resources["WarningBrush"];
                else
                    return Application.Current.Resources["SuccessBrush"];
            }
            return Application.Current.Resources["MaterialDesignBody"];
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
