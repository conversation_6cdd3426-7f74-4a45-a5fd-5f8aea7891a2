using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class InventoryViewModel : ObservableObject
    {
        private readonly IInventoryService _inventoryService;
        private readonly IProductService _productService;

        [ObservableProperty]
        private ObservableCollection<InventoryTransaction> transactions = new();

        [ObservableProperty]
        private ObservableCollection<Product> products = new();

        [ObservableProperty]
        private ObservableCollection<Product> lowStockProducts = new();

        [ObservableProperty]
        private InventoryTransaction? selectedTransaction;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool isAdjustmentMode = false;

        [ObservableProperty]
        private Product? selectedProductForAdjustment;

        [ObservableProperty]
        private decimal newStockQuantity = 0;

        [ObservableProperty]
        private string adjustmentReason = string.Empty;

        [ObservableProperty]
        private DateTime startDate = DateTime.Today.AddDays(-30);

        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        [ObservableProperty]
        private TransactionType? filterTransactionType;

        public InventoryViewModel(IInventoryService inventoryService, IProductService productService)
        {
            _inventoryService = inventoryService;
            _productService = productService;
            
            LoadDataAsync();
        }

        [RelayCommand]
        private async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                
                var transactionsData = await _inventoryService.GetTransactionsByDateRangeAsync(StartDate, EndDate);
                var productsData = await _productService.GetAllProductsAsync();
                var lowStockData = await _inventoryService.GetLowStockProductsAsync();
                
                Transactions.Clear();
                foreach (var transaction in transactionsData)
                {
                    Transactions.Add(transaction);
                }
                
                Products.Clear();
                foreach (var product in productsData)
                {
                    Products.Add(product);
                }
                
                LowStockProducts.Clear();
                foreach (var product in lowStockData)
                {
                    LowStockProducts.Add(product);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void StartStockAdjustment()
        {
            IsAdjustmentMode = true;
            SelectedProductForAdjustment = null;
            NewStockQuantity = 0;
            AdjustmentReason = string.Empty;
        }

        [RelayCommand]
        private async Task SaveStockAdjustmentAsync()
        {
            try
            {
                if (SelectedProductForAdjustment == null)
                {
                    MessageBox.Show("يرجى اختيار منتج", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (NewStockQuantity < 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(AdjustmentReason))
                {
                    MessageBox.Show("يرجى إدخال سبب التسوية", "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                IsLoading = true;

                await _inventoryService.AdjustStockAsync(
                    SelectedProductForAdjustment.Id, 
                    NewStockQuantity, 
                    AdjustmentReason);

                IsAdjustmentMode = false;
                await LoadDataAsync();
                
                MessageBox.Show("تم تسوية المخزون بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسوية المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void CancelStockAdjustment()
        {
            IsAdjustmentMode = false;
            SelectedProductForAdjustment = null;
            NewStockQuantity = 0;
            AdjustmentReason = string.Empty;
        }

        [RelayCommand]
        private async Task FilterTransactionsAsync()
        {
            try
            {
                IsLoading = true;
                
                var allTransactions = await _inventoryService.GetTransactionsByDateRangeAsync(StartDate, EndDate);
                
                if (FilterTransactionType.HasValue)
                {
                    allTransactions = allTransactions.Where(t => t.Type == FilterTransactionType.Value);
                }
                
                Transactions.Clear();
                foreach (var transaction in allTransactions)
                {
                    Transactions.Add(transaction);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصفية المعاملات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ShowTodayTransactionsAsync()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
            await FilterTransactionsAsync();
        }

        [RelayCommand]
        private async Task ShowThisWeekTransactionsAsync()
        {
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            StartDate = startOfWeek;
            EndDate = DateTime.Today;
            await FilterTransactionsAsync();
        }

        [RelayCommand]
        private async Task ShowThisMonthTransactionsAsync()
        {
            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            EndDate = DateTime.Today;
            await FilterTransactionsAsync();
        }

        [RelayCommand]
        private async Task ShowLowStockProductsAsync()
        {
            try
            {
                IsLoading = true;
                var lowStockData = await _inventoryService.GetLowStockProductsAsync();
                
                if (lowStockData.Any())
                {
                    var message = "المنتجات ذات المخزون المنخفض:\n\n";
                    foreach (var product in lowStockData.Take(10))
                    {
                        message += $"• {product.Name}: {product.CurrentStock} {product.Unit}\n";
                    }
                    
                    if (lowStockData.Count() > 10)
                    {
                        message += $"\n... و {lowStockData.Count() - 10} منتجات أخرى";
                    }
                    
                    MessageBox.Show(message, "تنبيه مخزون منخفض", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                else
                {
                    MessageBox.Show("لا توجد منتجات بمخزون منخفض", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ShowOutOfStockProductsAsync()
        {
            try
            {
                IsLoading = true;
                var outOfStockData = await _inventoryService.GetOutOfStockProductsAsync();
                
                if (outOfStockData.Any())
                {
                    var message = "المنتجات المنتهية من المخزون:\n\n";
                    foreach (var product in outOfStockData.Take(10))
                    {
                        message += $"• {product.Name}\n";
                    }
                    
                    if (outOfStockData.Count() > 10)
                    {
                        message += $"\n... و {outOfStockData.Count() - 10} منتجات أخرى";
                    }
                    
                    MessageBox.Show(message, "تنبيه مخزون منتهي", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                else
                {
                    MessageBox.Show("لا توجد منتجات منتهية من المخزون", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المنتجات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void ExportInventoryReport()
        {
            // TODO: Implement export functionality
            MessageBox.Show("تصدير التقرير سيكون متاح قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        partial void OnSelectedProductForAdjustmentChanged(Product? value)
        {
            if (value != null)
            {
                NewStockQuantity = value.CurrentStock;
            }
        }
    }
}
