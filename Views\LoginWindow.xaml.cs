using System.Windows;
using System.Windows.Controls;
using DateFactoryApp.ViewModels;

namespace DateFactoryApp.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            // Focus on username textbox when window loads
            Loaded += (s, e) => UsernameTextBox.Focus();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            // Simple login check
            string username = UsernameTextBox.Text;
            string password = PasswordBox.Password;

            if (username == "admin" && password == "admin")
            {
                // Login successful - open main window
                var mainWindow = new MainWindow();
                mainWindow.Show();
                this.Close();
            }
            else
            {
                // Show error message
                ErrorMessageTextBlock.Text = "اسم المستخدم أو كلمة المرور غير صحيحة";
                ErrorMessageTextBlock.Visibility = Visibility.Visible;
            }
        }
    }
}
