using System.Windows;
using System.Windows.Controls;
using DateFactoryApp.ViewModels;

namespace DateFactoryApp.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            // Create UserService manually for now
            var userService = new DateFactoryApp.Services.UserService(null!);
            DataContext = new LoginViewModel(userService);

            // Focus on username textbox when window loads
            Loaded += (s, e) => UsernameTextBox.Focus();

            // Handle password box binding
            PasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is LoginViewModel vm)
                {
                    vm.Password = PasswordBox.Password;
                }
            };
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is LoginViewModel vm)
            {
                await vm.LoginCommand.ExecuteAsync(null);
            }
        }
    }
}
