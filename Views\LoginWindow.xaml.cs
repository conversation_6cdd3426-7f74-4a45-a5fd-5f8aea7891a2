using System.Windows;
using System.Windows.Controls;
using DateFactoryApp.ViewModels;

namespace DateFactoryApp.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
            DataContext = App.GetService<LoginViewModel>();
            
            // Focus on username textbox when window loads
            Loaded += (s, e) => UsernameTextBox.Focus();
            
            // Handle password box binding
            PasswordBox.PasswordChanged += (s, e) =>
            {
                if (DataContext is LoginViewModel vm)
                {
                    vm.Password = PasswordBox.Password;
                }
            };
        }
    }
}
