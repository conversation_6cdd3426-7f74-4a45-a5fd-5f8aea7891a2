using System;
using System.Windows;
using MaterialDesignThemes.Wpf;
using Newtonsoft.Json;

namespace DateFactoryApp.Services
{
    public class ThemeService : IThemeService
    {
        private readonly PaletteHelper _paletteHelper;
        private bool _isDarkTheme;
        private string _currentPrimaryColor = "Blue";
        private string _currentSecondaryColor = "Lime";

        public bool IsDarkTheme => _isDarkTheme;

        public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        public ThemeService()
        {
            _paletteHelper = new PaletteHelper();
            _currentPrimaryColor = PrimaryColor.Brown;
            _currentSecondaryColor = SecondaryColor.Orange;

            LoadThemeSettings();
        }

        public void SetTheme(bool isDark)
        {
            _isDarkTheme = isDark;

            var theme = _paletteHelper.GetTheme();
            theme.SetBaseTheme(isDark ? Theme.Dark : Theme.Light);
            _paletteHelper.SetTheme(theme);

            SaveThemeSettings();
            OnThemeChanged();
        }

        public void ToggleTheme()
        {
            SetTheme(!_isDarkTheme);
        }

        public void SetPrimaryColor(string color)
        {
            _currentPrimaryColor = color;

            var theme = _paletteHelper.GetTheme();
            theme.SetPrimaryColor(color);
            _paletteHelper.SetTheme(theme);

            SaveThemeSettings();
            OnThemeChanged();
        }

        public void SetSecondaryColor(string color)
        {
            _currentSecondaryColor = color;

            var theme = _paletteHelper.GetTheme();
            theme.SetSecondaryColor(color);
            _paletteHelper.SetTheme(theme);

            SaveThemeSettings();
            OnThemeChanged();
        }

        public string GetCurrentPrimaryColor()
        {
            return _currentPrimaryColor;
        }

        public string GetCurrentSecondaryColor()
        {
            return _currentSecondaryColor;
        }

        public void SaveThemeSettings()
        {
            try
            {
                var settings = new ThemeSettings
                {
                    IsDarkTheme = _isDarkTheme,
                    PrimaryColor = _currentPrimaryColor.ToString(),
                    SecondaryColor = _currentSecondaryColor.ToString()
                };

                var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "DateFactory", "theme_settings.json");

                var directory = Path.GetDirectoryName(settingsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(settingsPath, json);
            }
            catch (Exception ex)
            {
                // Log error but don't throw - theme settings are not critical
                System.Diagnostics.Debug.WriteLine($"Failed to save theme settings: {ex.Message}");
            }
        }

        public void LoadThemeSettings()
        {
            try
            {
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "DateFactory", "theme_settings.json");

                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    var settings = JsonConvert.DeserializeObject<ThemeSettings>(json);

                    if (settings != null)
                    {
                        _isDarkTheme = settings.IsDarkTheme;

                        if (Enum.TryParse<PrimaryColor>(settings.PrimaryColor, out var primaryColor))
                        {
                            _currentPrimaryColor = primaryColor;
                        }

                        if (Enum.TryParse<SecondaryColor>(settings.SecondaryColor, out var secondaryColor))
                        {
                            _currentSecondaryColor = secondaryColor;
                        }

                        ApplyTheme();
                    }
                }
                else
                {
                    // Apply default theme
                    ApplyTheme();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw - use default theme
                System.Diagnostics.Debug.WriteLine($"Failed to load theme settings: {ex.Message}");
                ApplyTheme();
            }
        }

        private void ApplyTheme()
        {
            try
            {
                var theme = _paletteHelper.GetTheme();
                theme.SetBaseTheme(_isDarkTheme ? Theme.Dark : Theme.Light);
                theme.SetPrimaryColor(_currentPrimaryColor);
                theme.SetSecondaryColor(_currentSecondaryColor);
                _paletteHelper.SetTheme(theme);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to apply theme: {ex.Message}");
            }
        }

        private void OnThemeChanged()
        {
            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs
            {
                IsDarkTheme = _isDarkTheme,
                PrimaryColor = _currentPrimaryColor,
                SecondaryColor = _currentSecondaryColor
            });
        }
    }

    public class ThemeSettings
    {
        public bool IsDarkTheme { get; set; }
        public string PrimaryColor { get; set; } = "Brown";
        public string SecondaryColor { get; set; } = "Orange";
    }
}
