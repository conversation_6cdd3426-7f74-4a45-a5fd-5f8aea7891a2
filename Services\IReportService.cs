using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface IReportService
    {
        Task<SalesReport> GetSalesReportAsync(DateTime startDate, DateTime endDate);
        Task<InventoryReport> GetInventoryReportAsync();
        Task<ProfitReport> GetProfitReportAsync(DateTime startDate, DateTime endDate);
        Task<CustomerReport> GetCustomerReportAsync();
        Task<SupplierReport> GetSupplierReportAsync();
        Task<ProductPerformanceReport> GetProductPerformanceReportAsync(DateTime startDate, DateTime endDate);
        Task<DailySalesReport> GetDailySalesReportAsync(DateTime date);
        Task<MonthlySalesReport> GetMonthlySalesReportAsync(int year, int month);
        Task<bool> ExportReportToPdfAsync(object report, string filePath);
        Task<bool> ExportReportToExcelAsync(object report, string filePath);
    }

    public class SalesReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
        public int TotalTransactions { get; set; }
        public decimal AverageTransactionValue { get; set; }
        public List<SalesByDay> DailySales { get; set; } = new();
        public List<SalesByProduct> ProductSales { get; set; } = new();
        public List<SalesByCustomer> CustomerSales { get; set; } = new();
    }

    public class InventoryReport
    {
        public DateTime GeneratedDate { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public int OutOfStockProducts { get; set; }
        public List<ProductStock> ProductStocks { get; set; } = new();
        public List<Product> LowStockItems { get; set; } = new();
        public List<Product> OutOfStockItems { get; set; } = new();
    }

    public class ProfitReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal TotalCost { get; set; }
        public decimal GrossProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public List<ProfitByProduct> ProductProfits { get; set; } = new();
        public List<ProfitByDay> DailyProfits { get; set; } = new();
    }

    public class CustomerReport
    {
        public DateTime GeneratedDate { get; set; }
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; }
        public decimal TotalCustomerBalance { get; set; }
        public List<TopCustomer> TopCustomers { get; set; } = new();
        public List<Customer> CustomersWithBalance { get; set; } = new();
    }

    public class SupplierReport
    {
        public DateTime GeneratedDate { get; set; }
        public int TotalSuppliers { get; set; }
        public int ActiveSuppliers { get; set; }
        public decimal TotalSupplierBalance { get; set; }
        public List<TopSupplier> TopSuppliers { get; set; } = new();
        public List<Supplier> SuppliersWithBalance { get; set; } = new();
    }

    public class ProductPerformanceReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<ProductPerformance> Products { get; set; } = new();
    }

    public class DailySalesReport
    {
        public DateTime Date { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
        public int TransactionCount { get; set; }
        public List<Sale> Sales { get; set; } = new();
    }

    public class MonthlySalesReport
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
        public int TransactionCount { get; set; }
        public List<SalesByDay> DailySales { get; set; } = new();
    }

    // Supporting classes
    public class SalesByDay
    {
        public DateTime Date { get; set; }
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
    }

    public class SalesByProduct
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal Amount { get; set; }
    }

    public class SalesByCustomer
    {
        public string CustomerName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int TransactionCount { get; set; }
    }

    public class ProductStock
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal CurrentStock { get; set; }
        public decimal MinStock { get; set; }
        public decimal Value { get; set; }
    }

    public class ProfitByProduct
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public decimal Cost { get; set; }
        public decimal Profit { get; set; }
        public decimal Margin { get; set; }
    }

    public class ProfitByDay
    {
        public DateTime Date { get; set; }
        public decimal Profit { get; set; }
    }

    public class TopCustomer
    {
        public string Name { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int TransactionCount { get; set; }
    }

    public class TopSupplier
    {
        public string Name { get; set; } = string.Empty;
        public decimal TotalPurchases { get; set; }
        public int OrderCount { get; set; }
    }

    public class ProductPerformance
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal QuantitySold { get; set; }
        public decimal Revenue { get; set; }
        public decimal Profit { get; set; }
        public int TransactionCount { get; set; }
    }
}
