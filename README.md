# نظام إدارة مصنع ومحل التمور

نظام شامل لإدارة جميع عمليات مصنع ومحل التمور باستخدام C# WPF مع Material Design.

## 🌟 المميزات الرئيسية

### 📊 إدارة شاملة
- **إدارة المنتجات**: تصنيف وإدارة أنواع التمور المختلفة (خام، معالج، معبأ)
- **إدارة المبيعات**: نظام فواتير متكامل مع طرق دفع متعددة
- **إدارة المخزون**: تتبع المخزون مع تنبيهات الكمية المنخفضة
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء مع تتبع المبيعات
- **إدارة الموردين**: متابعة الموردين وأوامر الشراء
- **إدارة الإنتاج**: تسجيل عمليات التصنيع والتعبئة

### 🎨 واجهة عصرية
- **Material Design**: تصميم عصري وجذاب
- **الوضع الليلي/النهاري**: إمكانية التبديل بين الأوضاع
- **دعم اللغة العربية**: واجهة مصممة للغة العربية
- **تجربة مستخدم سلسة**: تنقل سهل وبديهي

### 🔐 نظام الصلاحيات
- **مدير**: صلاحيات كاملة
- **مشرف**: صلاحيات متوسطة
- **كاشير**: صلاحيات محدودة للمبيعات
- **عامل**: صلاحيات أساسية

### 📈 التقارير والإحصائيات
- **تقارير المبيعات**: يومية، أسبوعية، شهرية
- **تقارير المخزون**: حالة المخزون والمنتجات المنخفضة
- **تقارير الأرباح**: تحليل الربحية
- **تقارير العملاء والموردين**: أداء العملاء والموردين

### 💾 قاعدة البيانات المحلية
- **SQLite**: قاعدة بيانات محلية لا تحتاج تثبيت
- **ملف واحد**: سهولة النقل والنسخ الاحتياطي
- **أداء عالي**: استجابة سريعة

### ☁️ المزامنة التلقائية
- **مزامنة سحابية**: رفع البيانات تلقائياً عند توفر الإنترنت
- **العمل بدون إنترنت**: التطبيق يعمل بالكامل بدون إنترنت
- **تتبع التغييرات**: مزامنة البيانات المحدثة فقط

### 🔄 النسخ الاحتياطي
- **نسخ احتياطية تلقائية**: حفظ تلقائي للبيانات
- **استعادة سهلة**: استعادة البيانات بنقرة واحدة
- **ملفات مضغوطة**: توفير مساحة التخزين

## 🚀 متطلبات التشغيل

- **نظام التشغيل**: Windows 10/11
- **إطار العمل**: .NET 8.0
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 (الحد الأدنى)

## 📦 التثبيت والتشغيل

### 1. تحميل المتطلبات
```bash
# تأكد من تثبيت .NET 8.0 Runtime
# يمكن تحميله من: https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. استنساخ المشروع
```bash
git clone https://github.com/your-repo/DateFactoryApp.git
cd DateFactoryApp
```

### 3. استعادة الحزم
```bash
dotnet restore
```

### 4. بناء المشروع
```bash
dotnet build
```

### 5. تشغيل التطبيق
```bash
dotnet run
```

## 🔑 بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 📁 هيكل المشروع

```
DateFactoryApp/
├── Models/              # نماذج البيانات
├── ViewModels/          # منطق العرض
├── Views/               # واجهات المستخدم
├── Services/            # الخدمات والمنطق التجاري
├── Data/                # قاعدة البيانات والسياق
├── Resources/           # الموارد والأنماط
└── Helpers/             # المساعدات والأدوات
```

## 🛠️ التقنيات المستخدمة

- **C# 12**: لغة البرمجة الأساسية
- **WPF**: إطار عمل واجهة المستخدم
- **Material Design In XAML**: مكتبة التصميم
- **Entity Framework Core**: ORM لقاعدة البيانات
- **SQLite**: قاعدة البيانات المحلية
- **CommunityToolkit.Mvvm**: نمط MVVM
- **Serilog**: تسجيل الأحداث

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- **Users**: المستخدمين والصلاحيات
- **Products**: المنتجات والتصنيفات
- **Sales**: المبيعات والفواتير
- **Customers**: العملاء
- **Suppliers**: الموردين
- **InventoryTransactions**: حركات المخزون
- **ProductionRecords**: سجلات الإنتاج

## 🔧 الإعدادات والتخصيص

### إعدادات الثيم
- تغيير الألوان الأساسية والثانوية
- التبديل بين الوضع الليلي والنهاري
- حفظ التفضيلات تلقائياً

### إعدادات المزامنة
- تكوين نقطة النهاية للمزامنة
- تفعيل/إلغاء المزامنة التلقائية
- إعدادات الأمان والمصادقة

### إعدادات النسخ الاحتياطي
- تحديد مسار النسخ الاحتياطية
- جدولة النسخ التلقائية
- إعدادات الضغط والتشفير

## 🐛 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في قاعدة البيانات**
   - تأكد من وجود ملف `DateFactory.db`
   - تحقق من صلاحيات الكتابة في المجلد

2. **مشاكل المزامنة**
   - تحقق من اتصال الإنترنت
   - تأكد من صحة إعدادات المزامنة

3. **مشاكل الواجهة**
   - تأكد من تثبيت .NET 8.0 Runtime
   - أعد تشغيل التطبيق

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراسلة فريق التطوير
- مراجعة الوثائق التقنية

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📝 سجل التغييرات

راجع [CHANGELOG.md](CHANGELOG.md) لمعرفة آخر التحديثات والتحسينات.

---

**© 2024 نظام إدارة مصنع ومحل التمور - جميع الحقوق محفوظة**
