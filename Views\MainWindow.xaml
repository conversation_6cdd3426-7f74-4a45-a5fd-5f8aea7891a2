<Window x:Class="DateFactoryApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة مصنع ومحل التمور"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{materialDesign:MaterialDesignFont}"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top App Bar -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,8,16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Menu Button -->
                    <Button Grid.Column="0" Style="{StaticResource MaterialDesignIconButton}"
                          Command="{Binding ToggleMenuCommand}"
                          Foreground="White">
                        <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                    </Button>

                    <!-- Title -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="16,0">
                        <materialDesign:PackIcon Kind="Factory" Width="32" Height="32" Foreground="White" Margin="0,0,8,0"/>
                        <TextBlock Text="نظام إدارة مصنع ومحل التمور"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 Foreground="White" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- User Info and Actions -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Sync Status -->
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              Command="{Binding SyncCommand}"
                              ToolTip="مزامنة البيانات"
                              Foreground="White">
                            <materialDesign:PackIcon Kind="CloudSync" Width="20" Height="20"/>
                        </Button>

                        <!-- Notifications -->
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              Command="{Binding ShowNotificationsCommand}"
                              ToolTip="الإشعارات"
                              Foreground="White">
                            <materialDesign:PackIcon Kind="Bell" Width="20" Height="20"/>
                            <materialDesign:BadgedAssist.Badge>
                                <Border Background="{StaticResource ErrorBrush}" CornerRadius="8" Padding="4,2"
                                      Visibility="{Binding HasNotifications, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBlock Text="{Binding NotificationCount}" Foreground="White" FontSize="10"/>
                                </Border>
                            </materialDesign:BadgedAssist.Badge>
                        </Button>

                        <!-- Theme Toggle -->
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              Command="{Binding ToggleThemeCommand}"
                              ToolTip="تبديل الوضع الليلي/النهاري"
                              Foreground="White">
                            <materialDesign:PackIcon Kind="{Binding ThemeIcon}" Width="20" Height="20"/>
                        </Button>

                        <!-- User Menu -->
                        <materialDesign:PopupBox PlacementMode="BottomAndAlignRightEdges" StaysOpen="False">
                            <materialDesign:PopupBox.ToggleContent>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Account" Width="20" Height="20" Foreground="White" Margin="0,0,4,0"/>
                                    <TextBlock Text="{Binding CurrentUser.FullName}" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>
                            </materialDesign:PopupBox.ToggleContent>
                            <StackPanel>
                                <Button Content="الملف الشخصي" Command="{Binding ShowProfileCommand}"/>
                                <Button Content="إعدادات" Command="{Binding ShowSettingsCommand}"/>
                                <Separator/>
                                <Button Content="تسجيل الخروج" Command="{Binding LogoutCommand}"/>
                            </StackPanel>
                        </materialDesign:PopupBox>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Navigation Drawer -->
                <materialDesign:DrawerHost Grid.Column="0" Grid.ColumnSpan="2"
                                         IsLeftDrawerOpen="{Binding IsMenuOpen}"
                                         LeftDrawerBackground="{DynamicResource MaterialDesignCardBackground}">
                    <materialDesign:DrawerHost.LeftDrawerContent>
                        <Grid Width="280">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Navigation Header -->
                            <materialDesign:Card Grid.Row="0" Margin="8" Background="{StaticResource PrimaryBrush}">
                                <StackPanel Margin="16" Orientation="Vertical">
                                    <materialDesign:PackIcon Kind="Factory" Width="48" Height="48" Foreground="White" HorizontalAlignment="Center"/>
                                    <TextBlock Text="القائمة الرئيسية"
                                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                             Foreground="White" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- Navigation Menu -->
                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                <ListBox SelectedItem="{Binding SelectedMenuItem}"
                                       ItemsSource="{Binding MenuItems}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="8">
                                                <materialDesign:PackIcon Kind="{Binding Icon}" Width="20" Height="20" Margin="0,0,8,0"/>
                                                <TextBlock Text="{Binding Title}" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                            </ScrollViewer>

                            <!-- Navigation Footer -->
                            <StackPanel Grid.Row="2" Margin="16,8">
                                <Separator Margin="0,8"/>
                                <TextBlock Text="{Binding AppVersion}"
                                         Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                         HorizontalAlignment="Center"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Grid>
                    </materialDesign:DrawerHost.LeftDrawerContent>

                    <!-- Main Content Area -->
                    <Grid>
                        <ContentControl Content="{Binding CurrentViewModel}"/>

                        <!-- Loading Overlay -->
                        <Grid Background="Black" Opacity="0.5"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                           Width="50" Height="50" IsIndeterminate="True"/>
                                <TextBlock Text="{Binding LoadingMessage}"
                                         Foreground="White" HorizontalAlignment="Center" Margin="0,16,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:DrawerHost>
            </Grid>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}"
                     Foreground="{DynamicResource MaterialDesignBody}" Height="24">
                <StatusBar.ItemsPanel>
                    <ItemsPanelTemplate>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                        </Grid>
                    </ItemsPanelTemplate>
                </StatusBar.ItemsPanel>

                <StatusBarItem Grid.Column="0">
                    <TextBlock Text="{Binding StatusMessage}"/>
                </StatusBarItem>

                <StatusBarItem Grid.Column="1">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Account" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="{Binding CurrentUser.FullName}"/>
                    </StackPanel>
                </StatusBarItem>

                <StatusBarItem Grid.Column="2">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="{Binding SyncStatusIcon}" Width="16" Height="16" Margin="8,0,4,0"/>
                        <TextBlock Text="{Binding SyncStatusText}"/>
                    </StackPanel>
                </StatusBarItem>

                <StatusBarItem Grid.Column="3">
                    <TextBlock Text="{Binding CurrentDateTime, StringFormat='yyyy/MM/dd HH:mm'}" Margin="8,0"/>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </materialDesign:DialogHost>
</Window>
