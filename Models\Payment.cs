using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DateFactoryApp.Models
{
    public class Payment : BaseEntity
    {
        [Required]
        [MaxLength(50)]
        public string PaymentNumber { get; set; } = string.Empty;
        
        [Required]
        public DateTime PaymentDate { get; set; } = DateTime.Now;
        
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }
        
        [Required]
        public PaymentMethod Method { get; set; }
        
        [Required]
        public PaymentType Type { get; set; }
        
        [MaxLength(100)]
        public string? Reference { get; set; }
        
        [MaxLength(500)]
        public string? Notes { get; set; }
        
        public int? SaleId { get; set; }
        
        public int? PurchaseOrderId { get; set; }
        
        public int? CustomerId { get; set; }
        
        public int? SupplierId { get; set; }
        
        public int UserId { get; set; }
        
        // Navigation properties
        public virtual Sale? Sale { get; set; }
        public virtual PurchaseOrder? PurchaseOrder { get; set; }
        public virtual Customer? Customer { get; set; }
        public virtual Supplier? Supplier { get; set; }
        public virtual User User { get; set; } = null!;
    }

    public enum PaymentType
    {
        SalePayment = 1,        // دفعة مبيعات
        PurchasePayment = 2,    // دفعة مشتريات
        CustomerPayment = 3,    // دفعة عميل
        SupplierPayment = 4,    // دفعة مورد
        Expense = 5,            // مصروف
        Income = 6              // إيراد
    }
}
