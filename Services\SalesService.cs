using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class SalesService : ISalesService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;
        private readonly IInventoryService _inventoryService;

        public SalesService(AppDbContext context, IUserService userService, IInventoryService inventoryService)
        {
            _context = context;
            _userService = userService;
            _inventoryService = inventoryService;
        }

        public async Task<IEnumerable<Sale>> GetAllSalesAsync()
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.SaleDate >= startDate && s.SaleDate <= endDate)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Sale>> GetSalesByCustomerAsync(int customerId)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.CustomerId == customerId)
                .OrderByDescending(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<Sale?> GetSaleByIdAsync(int id)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Include(s => s.Payments)
                .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
        }

        public async Task<Sale?> GetSaleByInvoiceNumberAsync(string invoiceNumber)
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .FirstOrDefaultAsync(s => s.InvoiceNumber == invoiceNumber && !s.IsDeleted);
        }

        public async Task<Sale> CreateSaleAsync(Sale sale)
        {
            var currentUser = await _userService.GetCurrentUserAsync();
            
            sale.CreatedBy = currentUser?.Username;
            sale.CreatedAt = DateTime.Now;
            sale.UserId = currentUser?.Id ?? 1;

            if (string.IsNullOrEmpty(sale.InvoiceNumber))
            {
                sale.InvoiceNumber = await GenerateInvoiceNumberAsync();
            }

            // Calculate totals
            CalculateSaleTotals(sale);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Update inventory for each item
                foreach (var item in sale.SaleItems)
                {
                    await _inventoryService.CreateTransactionAsync(new InventoryTransaction
                    {
                        TransactionNumber = $"SALE-{sale.InvoiceNumber}",
                        TransactionDate = sale.SaleDate,
                        ProductId = item.ProductId,
                        Type = TransactionType.Sale,
                        Quantity = item.Quantity,
                        UnitCost = item.UnitPrice,
                        TotalCost = item.TotalPrice,
                        SaleId = sale.Id,
                        UserId = sale.UserId,
                        Reference = sale.InvoiceNumber
                    });
                }

                await transaction.CommitAsync();
                return sale;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<Sale> UpdateSaleAsync(Sale sale)
        {
            var existingSale = await _context.Sales
                .Include(s => s.SaleItems)
                .FirstOrDefaultAsync(s => s.Id == sale.Id);

            if (existingSale == null)
                throw new ArgumentException("الفاتورة غير موجودة");

            var currentUser = await _userService.GetCurrentUserAsync();

            // Update sale properties
            existingSale.CustomerId = sale.CustomerId;
            existingSale.SaleDate = sale.SaleDate;
            existingSale.PaymentMethod = sale.PaymentMethod;
            existingSale.Status = sale.Status;
            existingSale.Notes = sale.Notes;
            existingSale.UpdatedAt = DateTime.Now;
            existingSale.UpdatedBy = currentUser?.Username;

            // Update sale items
            _context.SaleItems.RemoveRange(existingSale.SaleItems);
            existingSale.SaleItems = sale.SaleItems;

            CalculateSaleTotals(existingSale);

            await _context.SaveChangesAsync();
            return existingSale;
        }

        public async Task<bool> DeleteSaleAsync(int id)
        {
            var sale = await _context.Sales
                .Include(s => s.SaleItems)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (sale == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Reverse inventory transactions
                foreach (var item in sale.SaleItems)
                {
                    await _inventoryService.CreateTransactionAsync(new InventoryTransaction
                    {
                        TransactionNumber = $"CANCEL-{sale.InvoiceNumber}",
                        TransactionDate = DateTime.Now,
                        ProductId = item.ProductId,
                        Type = TransactionType.Return,
                        Quantity = item.Quantity,
                        UnitCost = item.UnitPrice,
                        TotalCost = item.TotalPrice,
                        SaleId = sale.Id,
                        UserId = currentUser?.Id ?? 1,
                        Reference = $"إلغاء فاتورة {sale.InvoiceNumber}"
                    });
                }

                sale.IsDeleted = true;
                sale.Status = SaleStatus.Cancelled;
                sale.UpdatedAt = DateTime.Now;
                sale.UpdatedBy = currentUser?.Username;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"INV-{today:yyyyMMdd}-";
            
            var lastInvoice = await _context.Sales
                .Where(s => s.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(s => s.InvoiceNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastInvoice != null)
            {
                var numberPart = lastInvoice.InvoiceNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Sales.Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed);

            if (startDate.HasValue)
                query = query.Where(s => s.SaleDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(s => s.SaleDate <= endDate.Value);

            return await query.SumAsync(s => s.TotalAmount);
        }

        public async Task<decimal> GetTotalProfitAsync(DateTime? startDate = null, DateTime? endDate = null)
        {
            var query = _context.Sales
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed);

            if (startDate.HasValue)
                query = query.Where(s => s.SaleDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(s => s.SaleDate <= endDate.Value);

            var sales = await query.ToListAsync();
            
            decimal totalProfit = 0;
            foreach (var sale in sales)
            {
                foreach (var item in sale.SaleItems)
                {
                    var profit = (item.UnitPrice - item.Product.PurchasePrice) * item.Quantity;
                    totalProfit += profit;
                }
            }

            return totalProfit;
        }

        public async Task<IEnumerable<Sale>> GetPendingPaymentsAsync()
        {
            return await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.User)
                .Where(s => !s.IsDeleted && s.RemainingAmount > 0)
                .OrderBy(s => s.SaleDate)
                .ToListAsync();
        }

        public async Task<bool> ProcessPaymentAsync(int saleId, decimal amount, PaymentMethod method, string? reference = null)
        {
            var sale = await _context.Sales.FindAsync(saleId);
            if (sale == null || amount <= 0 || amount > sale.RemainingAmount)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            var payment = new Payment
            {
                PaymentNumber = await GeneratePaymentNumberAsync(),
                PaymentDate = DateTime.Now,
                Amount = amount,
                Method = method,
                Type = PaymentType.SalePayment,
                Reference = reference,
                SaleId = saleId,
                CustomerId = sale.CustomerId,
                UserId = currentUser?.Id ?? 1,
                CreatedBy = currentUser?.Username
            };

            _context.Payments.Add(payment);

            sale.PaidAmount += amount;
            sale.RemainingAmount -= amount;
            sale.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        private void CalculateSaleTotals(Sale sale)
        {
            sale.SubTotal = sale.SaleItems.Sum(item => item.TotalPrice);
            sale.TotalAmount = sale.SubTotal + sale.TaxAmount - sale.DiscountAmount;
            sale.RemainingAmount = sale.TotalAmount - sale.PaidAmount;

            foreach (var item in sale.SaleItems)
            {
                item.TotalPrice = item.Quantity * item.UnitPrice - item.DiscountAmount;
            }
        }

        private async Task<string> GeneratePaymentNumberAsync()
        {
            var today = DateTime.Today;
            var prefix = $"PAY-{today:yyyyMMdd}-";
            
            var lastPayment = await _context.Payments
                .Where(p => p.PaymentNumber.StartsWith(prefix))
                .OrderByDescending(p => p.PaymentNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastPayment != null)
            {
                var numberPart = lastPayment.PaymentNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }
    }
}
