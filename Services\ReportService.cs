using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class ReportService : IReportService
    {
        private readonly AppDbContext _context;

        public ReportService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<SalesReport> GetSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            var sales = await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed &&
                           s.SaleDate >= startDate && s.SaleDate <= endDate)
                .ToListAsync();

            var totalSales = sales.Sum(s => s.TotalAmount);
            var totalProfit = 0m;

            foreach (var sale in sales)
            {
                foreach (var item in sale.SaleItems)
                {
                    totalProfit += (item.UnitPrice - item.Product.PurchasePrice) * item.Quantity;
                }
            }

            var dailySales = sales
                .GroupBy(s => s.SaleDate.Date)
                .Select(g => new SalesByDay
                {
                    Date = g.Key,
                    Amount = g.Sum(s => s.TotalAmount),
                    TransactionCount = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            var productSales = sales
                .SelectMany(s => s.SaleItems)
                .GroupBy(si => si.Product.Name)
                .Select(g => new SalesByProduct
                {
                    ProductName = g.Key,
                    Quantity = g.Sum(si => si.Quantity),
                    Amount = g.Sum(si => si.TotalPrice)
                })
                .OrderByDescending(p => p.Amount)
                .ToList();

            var customerSales = sales
                .GroupBy(s => s.Customer.Name)
                .Select(g => new SalesByCustomer
                {
                    CustomerName = g.Key,
                    Amount = g.Sum(s => s.TotalAmount),
                    TransactionCount = g.Count()
                })
                .OrderByDescending(c => c.Amount)
                .ToList();

            return new SalesReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSales = totalSales,
                TotalProfit = totalProfit,
                TotalTransactions = sales.Count,
                AverageTransactionValue = sales.Count > 0 ? totalSales / sales.Count : 0,
                DailySales = dailySales,
                ProductSales = productSales,
                CustomerSales = customerSales
            };
        }

        public async Task<InventoryReport> GetInventoryReportAsync()
        {
            var products = await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted && p.IsActive)
                .ToListAsync();

            var totalValue = products.Sum(p => p.CurrentStock * p.PurchasePrice);
            var lowStockProducts = products.Where(p => p.CurrentStock <= p.MinStockLevel).ToList();
            var outOfStockProducts = products.Where(p => p.CurrentStock <= 0).ToList();

            var productStocks = products.Select(p => new ProductStock
            {
                ProductName = p.Name,
                CurrentStock = p.CurrentStock,
                MinStock = p.MinStockLevel,
                Value = p.CurrentStock * p.PurchasePrice
            }).ToList();

            return new InventoryReport
            {
                GeneratedDate = DateTime.Now,
                TotalInventoryValue = totalValue,
                TotalProducts = products.Count,
                LowStockProducts = lowStockProducts.Count,
                OutOfStockProducts = outOfStockProducts.Count,
                ProductStocks = productStocks,
                LowStockItems = lowStockProducts,
                OutOfStockItems = outOfStockProducts
            };
        }

        public async Task<ProfitReport> GetProfitReportAsync(DateTime startDate, DateTime endDate)
        {
            var sales = await _context.Sales
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed &&
                           s.SaleDate >= startDate && s.SaleDate <= endDate)
                .ToListAsync();

            var totalRevenue = sales.Sum(s => s.TotalAmount);
            var totalCost = 0m;
            var productProfits = new Dictionary<string, ProfitByProduct>();

            foreach (var sale in sales)
            {
                foreach (var item in sale.SaleItems)
                {
                    var itemCost = item.Product.PurchasePrice * item.Quantity;
                    var itemRevenue = item.TotalPrice;
                    var itemProfit = itemRevenue - itemCost;

                    totalCost += itemCost;

                    if (!productProfits.ContainsKey(item.Product.Name))
                    {
                        productProfits[item.Product.Name] = new ProfitByProduct
                        {
                            ProductName = item.Product.Name
                        };
                    }

                    var productProfit = productProfits[item.Product.Name];
                    productProfit.Revenue += itemRevenue;
                    productProfit.Cost += itemCost;
                    productProfit.Profit += itemProfit;
                }
            }

            foreach (var profit in productProfits.Values)
            {
                profit.Margin = profit.Revenue > 0 ? (profit.Profit / profit.Revenue) * 100 : 0;
            }

            var dailyProfits = sales
                .GroupBy(s => s.SaleDate.Date)
                .Select(g => new ProfitByDay
                {
                    Date = g.Key,
                    Profit = g.SelectMany(s => s.SaleItems)
                            .Sum(si => (si.UnitPrice - si.Product.PurchasePrice) * si.Quantity)
                })
                .OrderBy(d => d.Date)
                .ToList();

            var grossProfit = totalRevenue - totalCost;
            var profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

            return new ProfitReport
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalRevenue = totalRevenue,
                TotalCost = totalCost,
                GrossProfit = grossProfit,
                ProfitMargin = profitMargin,
                ProductProfits = productProfits.Values.OrderByDescending(p => p.Profit).ToList(),
                DailyProfits = dailyProfits
            };
        }

        public async Task<CustomerReport> GetCustomerReportAsync()
        {
            var customers = await _context.Customers
                .Where(c => !c.IsDeleted)
                .ToListAsync();

            var activeCustomers = customers.Where(c => c.IsActive).ToList();
            var customersWithBalance = customers.Where(c => c.CurrentBalance != 0).ToList();
            var totalBalance = customers.Sum(c => c.CurrentBalance);

            var topCustomers = await _context.Sales
                .Include(s => s.Customer)
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed)
                .GroupBy(s => s.Customer)
                .Select(g => new TopCustomer
                {
                    Name = g.Key.Name,
                    TotalPurchases = g.Sum(s => s.TotalAmount),
                    TransactionCount = g.Count()
                })
                .OrderByDescending(c => c.TotalPurchases)
                .Take(10)
                .ToListAsync();

            return new CustomerReport
            {
                GeneratedDate = DateTime.Now,
                TotalCustomers = customers.Count,
                ActiveCustomers = activeCustomers.Count,
                TotalCustomerBalance = totalBalance,
                TopCustomers = topCustomers,
                CustomersWithBalance = customersWithBalance
            };
        }

        public async Task<SupplierReport> GetSupplierReportAsync()
        {
            var suppliers = await _context.Suppliers
                .Where(s => !s.IsDeleted)
                .ToListAsync();

            var activeSuppliers = suppliers.Where(s => s.IsActive).ToList();
            var suppliersWithBalance = suppliers.Where(s => s.CurrentBalance != 0).ToList();
            var totalBalance = suppliers.Sum(s => s.CurrentBalance);

            var topSuppliers = await _context.PurchaseOrders
                .Include(po => po.Supplier)
                .Where(po => !po.IsDeleted && po.Status == PurchaseOrderStatus.Received)
                .GroupBy(po => po.Supplier)
                .Select(g => new TopSupplier
                {
                    Name = g.Key.Name,
                    TotalPurchases = g.Sum(po => po.TotalAmount),
                    OrderCount = g.Count()
                })
                .OrderByDescending(s => s.TotalPurchases)
                .Take(10)
                .ToListAsync();

            return new SupplierReport
            {
                GeneratedDate = DateTime.Now,
                TotalSuppliers = suppliers.Count,
                ActiveSuppliers = activeSuppliers.Count,
                TotalSupplierBalance = totalBalance,
                TopSuppliers = topSuppliers,
                SuppliersWithBalance = suppliersWithBalance
            };
        }

        public async Task<ProductPerformanceReport> GetProductPerformanceReportAsync(DateTime startDate, DateTime endDate)
        {
            var productPerformance = await _context.SaleItems
                .Include(si => si.Product)
                .Include(si => si.Sale)
                .Where(si => !si.IsDeleted && !si.Sale.IsDeleted && 
                            si.Sale.Status == SaleStatus.Completed &&
                            si.Sale.SaleDate >= startDate && si.Sale.SaleDate <= endDate)
                .GroupBy(si => si.Product)
                .Select(g => new ProductPerformance
                {
                    ProductName = g.Key.Name,
                    QuantitySold = g.Sum(si => si.Quantity),
                    Revenue = g.Sum(si => si.TotalPrice),
                    Profit = g.Sum(si => (si.UnitPrice - si.Product.PurchasePrice) * si.Quantity),
                    TransactionCount = g.Select(si => si.SaleId).Distinct().Count()
                })
                .OrderByDescending(p => p.Revenue)
                .ToListAsync();

            return new ProductPerformanceReport
            {
                StartDate = startDate,
                EndDate = endDate,
                Products = productPerformance
            };
        }

        public async Task<DailySalesReport> GetDailySalesReportAsync(DateTime date)
        {
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);

            var sales = await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed &&
                           s.SaleDate >= startDate && s.SaleDate < endDate)
                .ToListAsync();

            var totalSales = sales.Sum(s => s.TotalAmount);
            var totalProfit = 0m;

            foreach (var sale in sales)
            {
                foreach (var item in sale.SaleItems)
                {
                    totalProfit += (item.UnitPrice - item.Product.PurchasePrice) * item.Quantity;
                }
            }

            return new DailySalesReport
            {
                Date = date,
                TotalSales = totalSales,
                TotalProfit = totalProfit,
                TransactionCount = sales.Count,
                Sales = sales
            };
        }

        public async Task<MonthlySalesReport> GetMonthlySalesReportAsync(int year, int month)
        {
            var startDate = new DateTime(year, month, 1);
            var endDate = startDate.AddMonths(1);

            var sales = await _context.Sales
                .Where(s => !s.IsDeleted && s.Status == SaleStatus.Completed &&
                           s.SaleDate >= startDate && s.SaleDate < endDate)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Product)
                .ToListAsync();

            var totalSales = sales.Sum(s => s.TotalAmount);
            var totalProfit = 0m;

            foreach (var sale in sales)
            {
                foreach (var item in sale.SaleItems)
                {
                    totalProfit += (item.UnitPrice - item.Product.PurchasePrice) * item.Quantity;
                }
            }

            var dailySales = sales
                .GroupBy(s => s.SaleDate.Date)
                .Select(g => new SalesByDay
                {
                    Date = g.Key,
                    Amount = g.Sum(s => s.TotalAmount),
                    TransactionCount = g.Count()
                })
                .OrderBy(d => d.Date)
                .ToList();

            return new MonthlySalesReport
            {
                Year = year,
                Month = month,
                TotalSales = totalSales,
                TotalProfit = totalProfit,
                TransactionCount = sales.Count,
                DailySales = dailySales
            };
        }

        public async Task<bool> ExportReportToPdfAsync(object report, string filePath)
        {
            // This would require a PDF library like iTextSharp or similar
            // For now, we'll return a placeholder implementation
            try
            {
                // TODO: Implement PDF export functionality
                await Task.Delay(100); // Placeholder
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ExportReportToExcelAsync(object report, string filePath)
        {
            // This would require an Excel library like EPPlus or similar
            // For now, we'll return a placeholder implementation
            try
            {
                // TODO: Implement Excel export functionality
                await Task.Delay(100); // Placeholder
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
