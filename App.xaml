<Application x:Class="DateFactoryApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:helpers="clr-namespace:DateFactoryApp.Helpers"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Custom Styles will be added later -->
                <!-- <ResourceDictionary Source="Resources/Styles.xaml" /> -->
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Colors -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#8D6E63"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FF8A65"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#FFA726"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>

            <!-- Dark Theme Colors -->
            <SolidColorBrush x:Key="DarkPrimaryBrush" Color="#5D4037"/>
            <SolidColorBrush x:Key="DarkSecondaryBrush" Color="#6D4C41"/>
            <SolidColorBrush x:Key="DarkBackgroundBrush" Color="#212121"/>
            <SolidColorBrush x:Key="DarkSurfaceBrush" Color="#303030"/>

            <!-- Converters will be added later -->
            <!-- <helpers:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/> -->

        </ResourceDictionary>
    </Application.Resources>
</Application>
