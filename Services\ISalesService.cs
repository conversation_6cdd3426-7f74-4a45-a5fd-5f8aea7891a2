using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface ISalesService
    {
        Task<IEnumerable<Sale>> GetAllSalesAsync();
        Task<IEnumerable<Sale>> GetSalesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<Sale>> GetSalesByCustomerAsync(int customerId);
        Task<Sale?> GetSaleByIdAsync(int id);
        Task<Sale?> GetSaleByInvoiceNumberAsync(string invoiceNumber);
        Task<Sale> CreateSaleAsync(Sale sale);
        Task<Sale> UpdateSaleAsync(Sale sale);
        Task<bool> DeleteSaleAsync(int id);
        Task<string> GenerateInvoiceNumberAsync();
        Task<decimal> GetTotalSalesAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<decimal> GetTotalProfitAsync(DateTime? startDate = null, DateTime? endDate = null);
        Task<IEnumerable<Sale>> GetPendingPaymentsAsync();
        Task<bool> ProcessPaymentAsync(int saleId, decimal amount, PaymentMethod method, string? reference = null);
    }
}
