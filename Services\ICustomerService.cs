using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<Customer?> GetCustomerByCodeAsync(string code);
        Task<Customer> CreateCustomerAsync(Customer customer);
        Task<Customer> UpdateCustomerAsync(Customer customer);
        Task<bool> DeleteCustomerAsync(int id);
        Task<bool> IsCodeAvailableAsync(string code, int? excludeCustomerId = null);
        Task<IEnumerable<Customer>> GetCustomersWithBalanceAsync();
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task<bool> UpdateCustomerBalanceAsync(int customerId, decimal amount);
        Task<IEnumerable<Sale>> GetCustomerSalesAsync(int customerId);
        Task<string> GenerateCustomerCodeAsync();
    }
}
