using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Services;
using System.Collections.ObjectModel;
using System.Windows;

namespace DateFactoryApp.ViewModels
{
    public partial class ReportsViewModel : ObservableObject
    {
        private readonly IReportService _reportService;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private DateTime startDate = DateTime.Today.AddDays(-30);

        [ObservableProperty]
        private DateTime endDate = DateTime.Today;

        [ObservableProperty]
        private SalesReport? salesReport;

        [ObservableProperty]
        private InventoryReport? inventoryReport;

        [ObservableProperty]
        private ProfitReport? profitReport;

        [ObservableProperty]
        private CustomerReport? customerReport;

        [ObservableProperty]
        private SupplierReport? supplierReport;

        [ObservableProperty]
        private string selectedReportType = "Sales";

        public ObservableCollection<string> ReportTypes { get; } = new()
        {
            "Sales", "Inventory", "Profit", "Customer", "Supplier"
        };

        public ReportsViewModel(IReportService reportService)
        {
            _reportService = reportService;
        }

        [RelayCommand]
        private async Task GenerateSalesReportAsync()
        {
            try
            {
                IsLoading = true;
                SalesReport = await _reportService.GetSalesReportAsync(StartDate, EndDate);
                SelectedReportType = "Sales";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task GenerateInventoryReportAsync()
        {
            try
            {
                IsLoading = true;
                InventoryReport = await _reportService.GetInventoryReportAsync();
                SelectedReportType = "Inventory";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task GenerateProfitReportAsync()
        {
            try
            {
                IsLoading = true;
                ProfitReport = await _reportService.GetProfitReportAsync(StartDate, EndDate);
                SelectedReportType = "Profit";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير الأرباح: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task GenerateCustomerReportAsync()
        {
            try
            {
                IsLoading = true;
                CustomerReport = await _reportService.GetCustomerReportAsync();
                SelectedReportType = "Customer";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير العملاء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task GenerateSupplierReportAsync()
        {
            try
            {
                IsLoading = true;
                SupplierReport = await _reportService.GetSupplierReportAsync();
                SelectedReportType = "Supplier";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير الموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ExportToPdfAsync()
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تقرير_{SelectedReportType}_{DateTime.Now:yyyyMMdd}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    
                    object? report = SelectedReportType switch
                    {
                        "Sales" => SalesReport,
                        "Inventory" => InventoryReport,
                        "Profit" => ProfitReport,
                        "Customer" => CustomerReport,
                        "Supplier" => SupplierReport,
                        _ => null
                    };

                    if (report != null)
                    {
                        var success = await _reportService.ExportReportToPdfAsync(report, saveDialog.FileName);
                        if (success)
                        {
                            MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في تصدير التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task ExportToExcelAsync()
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    DefaultExt = "xlsx",
                    FileName = $"تقرير_{SelectedReportType}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    IsLoading = true;
                    
                    object? report = SelectedReportType switch
                    {
                        "Sales" => SalesReport,
                        "Inventory" => InventoryReport,
                        "Profit" => ProfitReport,
                        "Customer" => CustomerReport,
                        "Supplier" => SupplierReport,
                        _ => null
                    };

                    if (report != null)
                    {
                        var success = await _reportService.ExportReportToExcelAsync(report, saveDialog.FileName);
                        if (success)
                        {
                            MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("فشل في تصدير التقرير", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task GenerateTodayReportAsync()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today;
            await GenerateSalesReportAsync();
        }

        [RelayCommand]
        private async Task GenerateWeeklyReportAsync()
        {
            var startOfWeek = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            StartDate = startOfWeek;
            EndDate = DateTime.Today;
            await GenerateSalesReportAsync();
        }

        [RelayCommand]
        private async Task GenerateMonthlyReportAsync()
        {
            StartDate = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
            EndDate = DateTime.Today;
            await GenerateSalesReportAsync();
        }

        [RelayCommand]
        private async Task GenerateYearlyReportAsync()
        {
            StartDate = new DateTime(DateTime.Today.Year, 1, 1);
            EndDate = DateTime.Today;
            await GenerateSalesReportAsync();
        }

        [RelayCommand]
        private void PrintReport()
        {
            // TODO: Implement print functionality
            MessageBox.Show("الطباعة ستكون متاحة قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
