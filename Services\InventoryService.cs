using Microsoft.EntityFrameworkCore;
using DateFactoryApp.Data;
using DateFactoryApp.Models;

namespace DateFactoryApp.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly AppDbContext _context;
        private readonly IUserService _userService;

        public InventoryService(AppDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<IEnumerable<InventoryTransaction>> GetAllTransactionsAsync()
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.Supplier)
                .Include(t => t.User)
                .Where(t => !t.IsDeleted)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByProductAsync(int productId)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.Supplier)
                .Include(t => t.User)
                .Where(t => !t.IsDeleted && t.ProductId == productId)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<InventoryTransaction>> GetTransactionsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.Supplier)
                .Include(t => t.User)
                .Where(t => !t.IsDeleted && t.TransactionDate >= startDate && t.TransactionDate <= endDate)
                .OrderByDescending(t => t.TransactionDate)
                .ToListAsync();
        }

        public async Task<InventoryTransaction?> GetTransactionByIdAsync(int id)
        {
            return await _context.InventoryTransactions
                .Include(t => t.Product)
                .Include(t => t.Supplier)
                .Include(t => t.User)
                .FirstOrDefaultAsync(t => t.Id == id && !t.IsDeleted);
        }

        public async Task<InventoryTransaction> CreateTransactionAsync(InventoryTransaction transaction)
        {
            var currentUser = await _userService.GetCurrentUserAsync();
            var product = await _context.Products.FindAsync(transaction.ProductId);
            
            if (product == null)
                throw new ArgumentException("المنتج غير موجود");

            transaction.CreatedBy = currentUser?.Username;
            transaction.CreatedAt = DateTime.Now;
            transaction.StockBefore = product.CurrentStock;

            // Calculate stock after transaction
            switch (transaction.Type)
            {
                case TransactionType.Purchase:
                case TransactionType.Production:
                case TransactionType.Return:
                case TransactionType.Adjustment when transaction.Quantity > transaction.StockBefore:
                    transaction.StockAfter = transaction.StockBefore + Math.Abs(transaction.Quantity);
                    product.CurrentStock = transaction.StockAfter;
                    break;
                
                case TransactionType.Sale:
                case TransactionType.Damage:
                case TransactionType.Expired:
                case TransactionType.Adjustment when transaction.Quantity < transaction.StockBefore:
                    transaction.StockAfter = transaction.StockBefore - Math.Abs(transaction.Quantity);
                    product.CurrentStock = transaction.StockAfter;
                    break;
                
                case TransactionType.Adjustment:
                    transaction.StockAfter = transaction.Quantity;
                    product.CurrentStock = transaction.StockAfter;
                    break;
            }

            if (string.IsNullOrEmpty(transaction.TransactionNumber))
            {
                transaction.TransactionNumber = await GenerateTransactionNumberAsync(transaction.Type);
            }

            _context.InventoryTransactions.Add(transaction);
            await _context.SaveChangesAsync();
            
            return transaction;
        }

        public async Task<InventoryTransaction> UpdateTransactionAsync(InventoryTransaction transaction)
        {
            var existingTransaction = await _context.InventoryTransactions.FindAsync(transaction.Id);
            if (existingTransaction == null)
                throw new ArgumentException("المعاملة غير موجودة");

            var currentUser = await _userService.GetCurrentUserAsync();

            // Reverse the old transaction effect
            var product = await _context.Products.FindAsync(existingTransaction.ProductId);
            if (product != null)
            {
                product.CurrentStock = existingTransaction.StockBefore;
            }

            // Update transaction properties
            existingTransaction.TransactionDate = transaction.TransactionDate;
            existingTransaction.Quantity = transaction.Quantity;
            existingTransaction.UnitCost = transaction.UnitCost;
            existingTransaction.TotalCost = transaction.TotalCost;
            existingTransaction.Notes = transaction.Notes;
            existingTransaction.Reference = transaction.Reference;
            existingTransaction.UpdatedAt = DateTime.Now;
            existingTransaction.UpdatedBy = currentUser?.Username;

            // Apply the new transaction effect
            if (product != null)
            {
                switch (existingTransaction.Type)
                {
                    case TransactionType.Purchase:
                    case TransactionType.Production:
                    case TransactionType.Return:
                        existingTransaction.StockAfter = existingTransaction.StockBefore + existingTransaction.Quantity;
                        break;
                    case TransactionType.Sale:
                    case TransactionType.Damage:
                    case TransactionType.Expired:
                        existingTransaction.StockAfter = existingTransaction.StockBefore - existingTransaction.Quantity;
                        break;
                    case TransactionType.Adjustment:
                        existingTransaction.StockAfter = existingTransaction.Quantity;
                        break;
                }

                product.CurrentStock = existingTransaction.StockAfter;
            }

            await _context.SaveChangesAsync();
            return existingTransaction;
        }

        public async Task<bool> DeleteTransactionAsync(int id)
        {
            var transaction = await _context.InventoryTransactions.FindAsync(id);
            if (transaction == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            // Reverse the transaction effect on stock
            var product = await _context.Products.FindAsync(transaction.ProductId);
            if (product != null)
            {
                product.CurrentStock = transaction.StockBefore;
            }

            transaction.IsDeleted = true;
            transaction.UpdatedAt = DateTime.Now;
            transaction.UpdatedBy = currentUser?.Username;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateTransactionNumberAsync(TransactionType type)
        {
            var today = DateTime.Today;
            var prefix = type switch
            {
                TransactionType.Purchase => $"PUR-{today:yyyyMMdd}-",
                TransactionType.Sale => $"SALE-{today:yyyyMMdd}-",
                TransactionType.Production => $"PROD-{today:yyyyMMdd}-",
                TransactionType.Adjustment => $"ADJ-{today:yyyyMMdd}-",
                TransactionType.Transfer => $"TRF-{today:yyyyMMdd}-",
                TransactionType.Return => $"RET-{today:yyyyMMdd}-",
                TransactionType.Damage => $"DMG-{today:yyyyMMdd}-",
                TransactionType.Expired => $"EXP-{today:yyyyMMdd}-",
                _ => $"TXN-{today:yyyyMMdd}-"
            };

            var lastTransaction = await _context.InventoryTransactions
                .Where(t => t.TransactionNumber.StartsWith(prefix))
                .OrderByDescending(t => t.TransactionNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastTransaction != null)
            {
                var numberPart = lastTransaction.TransactionNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        public async Task<decimal> GetCurrentStockAsync(int productId)
        {
            var product = await _context.Products.FindAsync(productId);
            return product?.CurrentStock ?? 0;
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted && p.IsActive && p.CurrentStock <= p.MinStockLevel)
                .OrderBy(p => p.CurrentStock)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> GetOutOfStockProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Category)
                .Where(p => !p.IsDeleted && p.IsActive && p.CurrentStock <= 0)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<bool> AdjustStockAsync(int productId, decimal newQuantity, string reason)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null)
                return false;

            var currentUser = await _userService.GetCurrentUserAsync();

            var transaction = new InventoryTransaction
            {
                TransactionNumber = await GenerateTransactionNumberAsync(TransactionType.Adjustment),
                TransactionDate = DateTime.Now,
                ProductId = productId,
                Type = TransactionType.Adjustment,
                Quantity = newQuantity,
                UnitCost = product.PurchasePrice,
                TotalCost = newQuantity * product.PurchasePrice,
                StockBefore = product.CurrentStock,
                StockAfter = newQuantity,
                Notes = reason,
                UserId = currentUser?.Id ?? 1,
                CreatedBy = currentUser?.Username
            };

            _context.InventoryTransactions.Add(transaction);
            product.CurrentStock = newQuantity;
            product.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
