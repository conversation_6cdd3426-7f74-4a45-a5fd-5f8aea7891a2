using MaterialDesignThemes.Wpf;

namespace DateFactoryApp.Services
{
    public interface IThemeService
    {
        bool IsDarkTheme { get; }
        void SetTheme(bool isDark);
        void ToggleTheme();
        void SetPrimaryColor(PrimaryColor color);
        void SetSecondaryColor(SecondaryColor color);
        PrimaryColor GetCurrentPrimaryColor();
        SecondaryColor GetCurrentSecondaryColor();
        void SaveThemeSettings();
        void LoadThemeSettings();
        event EventHandler<ThemeChangedEventArgs>? ThemeChanged;
    }

    public class ThemeChangedEventArgs : EventArgs
    {
        public bool IsDarkTheme { get; set; }
        public PrimaryColor PrimaryColor { get; set; }
        public SecondaryColor SecondaryColor { get; set; }
    }
}
