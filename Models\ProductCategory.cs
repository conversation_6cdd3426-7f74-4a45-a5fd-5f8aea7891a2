using System.ComponentModel.DataAnnotations;

namespace DateFactoryApp.Models
{
    public class ProductCategory : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(500)]
        public string? Description { get; set; }
        
        [MaxLength(50)]
        public string? Code { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        [MaxLength(255)]
        public string? ImagePath { get; set; }
        
        public int SortOrder { get; set; } = 0;
        
        // Navigation properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
