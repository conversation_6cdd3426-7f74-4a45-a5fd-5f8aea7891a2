using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using DateFactoryApp.Models;
using DateFactoryApp.Services;
using DateFactoryApp.Views;
using MaterialDesignThemes.Wpf;

namespace DateFactoryApp.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly IUserService _userService;
        private readonly IThemeService _themeService;
        private readonly ISyncService _syncService;
        private readonly System.Timers.Timer _clockTimer;

        [ObservableProperty]
        private User? currentUser;

        [ObservableProperty]
        private bool isMenuOpen = true;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string loadingMessage = string.Empty;

        [ObservableProperty]
        private string statusMessage = "جاهز";

        [ObservableProperty]
        private DateTime currentDateTime = DateTime.Now;

        [ObservableProperty]
        private string appVersion = "الإصدار 1.0";

        [ObservableProperty]
        private bool hasNotifications = false;

        [ObservableProperty]
        private int notificationCount = 0;

        [ObservableProperty]
        private PackIconKind themeIcon = PackIconKind.WeatherNight;

        [ObservableProperty]
        private PackIconKind syncStatusIcon = PackIconKind.CloudOff;

        [ObservableProperty]
        private string syncStatusText = "غير متصل";

        [ObservableProperty]
        private object? currentViewModel;

        [ObservableProperty]
        private MenuItem? selectedMenuItem;

        public ObservableCollection<MenuItem> MenuItems { get; } = new();

        public MainViewModel(IUserService userService, IThemeService themeService, ISyncService syncService)
        {
            _userService = userService;
            _themeService = themeService;
            _syncService = syncService;

            InitializeAsync();
            InitializeMenuItems();
            InitializeClock();
            InitializeTheme();
            InitializeSyncStatus();
        }

        private async void InitializeAsync()
        {
            CurrentUser = await _userService.GetCurrentUserAsync();
            StatusMessage = $"مرحباً {CurrentUser?.FullName}";

            // Load dashboard by default
            SelectedMenuItem = MenuItems.FirstOrDefault();
            await LoadSelectedView();
        }

        private void InitializeMenuItems()
        {
            MenuItems.Add(new MenuItem { Title = "لوحة التحكم", Icon = PackIconKind.ViewDashboard, ViewType = typeof(DashboardViewModel) });
            MenuItems.Add(new MenuItem { Title = "المبيعات", Icon = PackIconKind.CashRegister, ViewType = typeof(SalesViewModel) });
            MenuItems.Add(new MenuItem { Title = "المنتجات", Icon = PackIconKind.Package, ViewType = typeof(ProductsViewModel) });
            MenuItems.Add(new MenuItem { Title = "المخزون", Icon = PackIconKind.Warehouse, ViewType = typeof(InventoryViewModel) });
            MenuItems.Add(new MenuItem { Title = "العملاء", Icon = PackIconKind.AccountGroup, ViewType = typeof(CustomersViewModel) });
            MenuItems.Add(new MenuItem { Title = "الموردين", Icon = PackIconKind.TruckDelivery, ViewType = typeof(SuppliersViewModel) });
            MenuItems.Add(new MenuItem { Title = "الإنتاج", Icon = PackIconKind.Factory, ViewType = typeof(ProductionViewModel) });
            MenuItems.Add(new MenuItem { Title = "التقارير", Icon = PackIconKind.ChartLine, ViewType = typeof(ReportsViewModel) });
            MenuItems.Add(new MenuItem { Title = "الإعدادات", Icon = PackIconKind.Settings, ViewType = typeof(SettingsViewModel) });
        }

        private void InitializeClock()
        {
            var clockTimer = new System.Timers.Timer(1000);
            clockTimer.Elapsed += (s, e) => CurrentDateTime = DateTime.Now;
            clockTimer.Start();
        }

        private void InitializeTheme()
        {
            _themeService.ThemeChanged += OnThemeChanged;
            UpdateThemeIcon();
        }

        private async void InitializeSyncStatus()
        {
            await UpdateSyncStatus();

            // Update sync status every 30 seconds
            var syncTimer = new System.Timers.Timer(30000);
            syncTimer.Elapsed += async (s, e) => await UpdateSyncStatus();
            syncTimer.Start();
        }

        [RelayCommand]
        private void ToggleMenu()
        {
            IsMenuOpen = !IsMenuOpen;
        }

        [RelayCommand]
        private async Task SyncAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "جاري مزامنة البيانات...";

                var success = await _syncService.SyncToCloudAsync();
                StatusMessage = success ? "تمت المزامنة بنجاح" : "فشلت المزامنة";

                await UpdateSyncStatus();
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في المزامنة: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                LoadingMessage = string.Empty;
            }
        }

        [RelayCommand]
        private void ShowNotifications()
        {
            // TODO: Implement notifications dialog
            MessageBox.Show("الإشعارات ستكون متاحة قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void ToggleTheme()
        {
            _themeService.ToggleTheme();
        }

        [RelayCommand]
        private void ShowProfile()
        {
            // TODO: Implement profile dialog
            MessageBox.Show("الملف الشخصي سيكون متاح قريباً", "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        [RelayCommand]
        private void ShowSettings()
        {
            SelectedMenuItem = MenuItems.FirstOrDefault(m => m.ViewType == typeof(SettingsViewModel));
        }

        [RelayCommand]
        private void Logout()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new LoginWindow();
                loginWindow.Show();

                Application.Current.Windows.OfType<MainWindow>().FirstOrDefault()?.Close();
            }
        }

        partial void OnSelectedMenuItemChanged(MenuItem? value)
        {
            if (value != null)
            {
                _ = LoadSelectedView();
            }
        }

        private async Task LoadSelectedView()
        {
            if (SelectedMenuItem?.ViewType == null) return;

            try
            {
                IsLoading = true;
                LoadingMessage = "جاري تحميل الصفحة...";

                // Create view model instance
                var viewModel = Activator.CreateInstance(SelectedMenuItem.ViewType);
                CurrentViewModel = viewModel;

                StatusMessage = $"تم تحميل {SelectedMenuItem.Title}";
            }
            catch (Exception ex)
            {
                StatusMessage = $"خطأ في تحميل الصفحة: {ex.Message}";
                MessageBox.Show($"حدث خطأ أثناء تحميل الصفحة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
                LoadingMessage = string.Empty;
            }
        }

        private void OnThemeChanged(object? sender, Services.ThemeChangedEventArgs e)
        {
            UpdateThemeIcon();
        }

        private void UpdateThemeIcon()
        {
            ThemeIcon = _themeService.IsDarkTheme ? PackIconKind.WeatherSunny : PackIconKind.WeatherNight;
        }

        private async Task UpdateSyncStatus()
        {
            try
            {
                var syncStatus = await _syncService.GetSyncStatusAsync();

                SyncStatusIcon = syncStatus.IsOnline ? PackIconKind.CloudCheck : PackIconKind.CloudOff;
                SyncStatusText = syncStatus.IsOnline ? "متصل" : "غير متصل";

                if (syncStatus.PendingChanges > 0)
                {
                    SyncStatusIcon = PackIconKind.CloudSync;
                    SyncStatusText = $"في انتظار المزامنة ({syncStatus.PendingChanges})";
                }
            }
            catch
            {
                SyncStatusIcon = PackIconKind.CloudOff;
                SyncStatusText = "غير متصل";
            }
        }

        protected override void OnPropertyChanged(System.ComponentModel.PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);

            if (e.PropertyName == nameof(CurrentDateTime))
            {
                // Update on UI thread
                Application.Current?.Dispatcher.Invoke(() => { });
            }
        }
    }

    public class MenuItem
    {
        public string Title { get; set; } = string.Empty;
        public PackIconKind Icon { get; set; }
        public Type? ViewType { get; set; }
    }

    // Placeholder ViewModels - these will be implemented later
    public class ProductionViewModel { }
}
